import axios from 'axios';
import type { ApiResponse, User, Book, Borrow, Review } from '@/types';
import { API_BASE_URL } from '@/config';

const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 5000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 请求拦截器
api.interceptors.request.use(
    config => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    response => {
        return response;
    },
    error => {
        if (error.response?.status === 401) {
            // 未授权，清除用户信息并跳转到登录页
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

export const authService = {
    login: (username: string, password: string) => {
        return api.post<ApiResponse<User>>('/users/login', { username, password });
    },
    register: (user: User) => {
        return api.post<ApiResponse<User>>('/users', user);
    },
    logout: () => {
        return api.post<ApiResponse<void>>('/users/logout');
    },
    getCurrentUser: () => {
        return api.get<ApiResponse<User>>('/users/current');
    }
};

export const userService = {
    getAllUsers: () => {
        return api.get<ApiResponse<User[]>>('/users');
    },
    getUserById: (id: number) => {
        return api.get<ApiResponse<User>>(`/users/${id}`);
    },
    createUser: (user: User) => {
        return api.post<ApiResponse<User>>('/users', user);
    },
    updateUser: (id: number, user: User) => {
        return api.put<ApiResponse<User>>(`/users/${id}`, user);
    },
    updatePassword: (id: number, oldPassword: string, newPassword: string) => {
        return api.put<ApiResponse<void>>(`/users/${id}/password`, { oldPassword, newPassword });
    },
    updateStatus: (id: number, status: number) => {
        return api.put<ApiResponse<void>>(`/users/${id}/status`, { status });
    },
    deleteUser: (id: number) => {
        return api.delete<ApiResponse<void>>(`/users/${id}`);
    }
};

export const bookService = {
    getAllBooks: () => {
        return api.get<ApiResponse<Book[]>>('/books');
    },
    getBookById: (id: number) => {
        return api.get<ApiResponse<Book>>(`/books/${id}`);
    },
    createBook: (book: Book) => {
        return api.post<ApiResponse<Book>>('/books', book);
    },
    updateBook: (id: number, book: Book) => {
        return api.put<ApiResponse<Book>>(`/books/${id}`, book);
    },
    deleteBook: (id: number) => {
        return api.delete<ApiResponse<void>>(`/books/${id}`);
    },
    borrowBook: (bookId: number) => {
        return api.post<ApiResponse<void>>(`/books/${bookId}/borrow`);
    },
    returnBook: (bookId: number) => {
        return api.post<ApiResponse<void>>(`/books/${bookId}/return`);
    }
};

export const borrowService = {
    getAllBorrows: () => {
        return api.get<ApiResponse<Borrow[]>>('/borrows');
    },
    getBorrowById: (id: number) => {
        return api.get<ApiResponse<Borrow>>(`/borrows/${id}`);
    },
    createBorrow: (data: { 
        readerId: number; 
        bookId: number; 
        borrowDate: string;  // ISO 8601 格式的日期时间字符串
        dueDate: string;     // ISO 8601 格式的日期时间字符串
        status: number;      // 1: 借阅中, 2: 已归还, 3: 已逾期, 等等
    }) => {
        return api.post<ApiResponse<Borrow>>('/borrows', data);
    },
    updateBorrow: (id: number, data: { status: string; returnDate?: string; dueDate?: string }) => {
        return api.put<ApiResponse<Borrow>>(`/borrows/${id}`, data);
    },
    deleteBorrow: (id: number) => {
        return api.delete<ApiResponse<void>>(`/borrows/${id}`);
    },
    renewBorrow: (id: number) => {
        return api.post<ApiResponse<Borrow>>(`/borrows/${id}/renew`);
    }
};

export const reviewService = {
    getBookReviews: (bookId: number) => {
        // 使用模拟数据
        console.log(`获取图书${bookId}的评论`);
        const mockReviews = [
            {
                id: 1,
                bookId: bookId,
                userId: 1,
                userName: '张三',
                rating: 5,
                comment: '这本书非常好，内容丰富，讲解清晰。',
                createdTime: '2023-04-15T10:20:30'
            },
            {
                id: 2,
                bookId: bookId,
                userId: 2,
                userName: '李四',
                rating: 4,
                comment: '整体不错，但有些地方讲解不够详细。',
                createdTime: '2023-04-16T14:25:10'
            },
            {
                id: 3,
                bookId: bookId,
                userId: 3,
                userName: '王五',
                rating: 5,
                comment: '非常推荐这本书，对我帮助很大。',
                createdTime: '2023-04-17T09:15:45'
            }
        ];
        
        return Promise.resolve({
            data: {
                code: 200,
                message: '操作成功',
                data: mockReviews,
                success: true
            }
        });
    },
    addReview: (bookId: number, review: { rating: number, comment: string }) => {
        // 模拟添加评论
        console.log(`为图书${bookId}添加评论:`, review);
        const mockReview = {
            id: Math.floor(Math.random() * 1000),
            bookId: bookId,
            userId: 1,
            userName: '当前用户',
            rating: review.rating,
            comment: review.comment,
            createdTime: new Date().toISOString()
        };
        
        return Promise.resolve({
            data: {
                code: 200,
                message: '评论添加成功',
                data: mockReview,
                success: true
            }
        });
    },
    updateReview: (bookId: number, reviewId: number, review: { rating: number, comment: string }) => {
        // 模拟更新评论
        console.log(`更新图书${bookId}的评论${reviewId}:`, review);
        const mockReview = {
            id: reviewId,
            bookId: bookId,
            userId: 1,
            userName: '当前用户',
            rating: review.rating,
            comment: review.comment,
            createdTime: new Date().toISOString()
        };
        
        return Promise.resolve({
            data: {
                code: 200,
                message: '评论更新成功',
                data: mockReview,
                success: true
            }
        });
    },
    deleteReview: (bookId: number, reviewId: number) => {
        // 模拟删除评论
        console.log(`删除图书${bookId}的评论${reviewId}`);
        
        return Promise.resolve({
            data: {
                code: 200,
                message: '评论删除成功',
                data: null,
                success: true
            }
        });
    }
};

export const statsService = {
    getBorrowStats: (period?: string) => {
        return api.get<ApiResponse<any>>('/stats/borrows', { params: { period } });
    },
    getPopularBooks: (timeRange?: string) => {
        return api.get<ApiResponse<any>>('/stats/books/popular', { params: { timeRange } });
    },
    exportReport: (type: string, timeRange?: string) => {
        return api.get<ApiResponse<string>>('/stats/reports/export', { params: { type, timeRange } });
    }
}; 