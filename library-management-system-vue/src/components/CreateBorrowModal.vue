<!-- 新增借阅弹窗 -->
<template>
  <div class="modal d-block" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">新增借阅</h5>
          <button 
            type="button" 
            class="btn-close" 
            @click="$emit('close')"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSubmit">
            <div class="mb-3">
              <label for="userId" class="form-label">用户</label>
              <select 
                class="form-select" 
                id="userId" 
                v-model="formData.userId"
                required
              >
                <option value="">请选择用户</option>
                <option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.username }}
                </option>
              </select>
            </div>
            <div class="mb-3">
              <label for="bookId" class="form-label">图书</label>
              <select 
                class="form-select" 
                id="bookId" 
                v-model="formData.bookId"
                required
              >
                <option value="">请选择图书</option>
                <option v-for="book in books" :key="book.id" :value="book.id">
                  {{ book.title }} (库存: {{ book.availableCopies }}本)
                </option>
              </select>
            </div>
            <div class="mb-3">
              <label for="borrowDate" class="form-label">借阅日期</label>
              <input 
                type="date" 
                class="form-control" 
                id="borrowDate" 
                v-model="formData.borrowDate"
                required
              >
            </div>
            <div class="mb-3">
              <label for="dueDate" class="form-label">应还日期</label>
              <input 
                type="date" 
                class="form-control" 
                id="dueDate" 
                v-model="formData.dueDate"
                required
              >
            </div>
            <div v-if="error" class="alert alert-danger" role="alert">
              {{ error }}
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button 
            type="button" 
            class="btn btn-secondary" 
            @click="$emit('close')"
          >
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            @click="handleSubmit"
            :disabled="isSubmitting || !isFormValid"
          >
            <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
            确认
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { borrowService, userService, bookService } from '@/services/api';

const props = defineProps<{
  onSuccess: () => void;
}>();

const emit = defineEmits(['close']);

const users = ref([]);
const books = ref([]);
const error = ref('');
const isSubmitting = ref(false);

const formData = ref({
  userId: '',
  bookId: '',
  borrowDate: new Date().toISOString().split('T')[0],
  dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 默认14天后
});

const isFormValid = computed(() => {
  return formData.value.userId && 
         formData.value.bookId && 
         formData.value.borrowDate && 
         formData.value.dueDate;
});

// 获取用户列表
const fetchUsers = async () => {
  try {
    const response = await userService.getAllUsers();
    console.log('用户数据:', response.data);
    if (response.data.success) {
      // 直接使用返回的数据
      users.value = response.data.data;
      console.log('处理后的用户数据:', users.value);
    }
  } catch (e) {
    console.error('获取用户列表失败:', e);
    error.value = '获取用户列表失败，请稍后再试';
  }
};

// 获取可借阅图书列表
const fetchBooks = async () => {
  try {
    const response = await bookService.getAllBooks();
    console.log('原始图书数据:', response.data);
    if (response.data.success) {
      // 直接使用返回的数据
      books.value = response.data.data;
      console.log('处理后的图书数据:', books.value);
    }
  } catch (e) {
    console.error('获取图书列表失败:', e);
    error.value = '获取图书列表失败，请稍后再试';
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!isFormValid.value) {
    error.value = '请填写所有必填字段';
    return;
  }
  
  isSubmitting.value = true;
  error.value = '';
  
  try {
    // 将日期转换为 LocalDateTime 格式
    const formatToDateTime = (dateStr: string) => {
      return `${dateStr}T00:00:00`;  // 添加时间部分
    };

    const borrowData = {
      readerId: Number(formData.value.userId),
      bookId: Number(formData.value.bookId),
      borrowDate: formatToDateTime(formData.value.borrowDate),
      dueDate: formatToDateTime(formData.value.dueDate),
      status: 1  // 使用整数表示借阅状态：1 表示借阅中
    };

    // 打印提交的数据，用于调试
    console.log('提交的借阅数据:', borrowData);

    const response = await borrowService.createBorrow(borrowData);
    
    if (response.data.success) {
      props.onSuccess();
      emit('close');
    } else {
      error.value = response.data.message || '新增借阅失败，请稍后再试';
    }
  } catch (e: any) {
    console.error('新增借阅失败:', e);
    error.value = e.response?.data?.message || '新增借阅失败，请稍后再试';
  } finally {
    isSubmitting.value = false;
  }
};

// 组件挂载时获取数据
fetchUsers();
fetchBooks();
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}
</style> 