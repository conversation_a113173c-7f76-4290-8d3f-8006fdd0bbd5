<template>
  <div class="login-container">
    <div class="card login-card">
      <div class="card-header text-center">
        <h4 class="my-2">图书管理系统</h4>
      </div>
      <div class="card-body">
        <h5 class="card-title">用户登录</h5>
        <form @submit.prevent="handleLogin">
          <div class="mb-3">
            <label for="username" class="form-label">用户名</label>
            <input 
              type="text" 
              class="form-control" 
              id="username" 
              v-model="username" 
              required
            >
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">密码</label>
            <input 
              type="password" 
              class="form-control" 
              id="password" 
              v-model="password" 
              required
            >
          </div>
          <div class="mb-3 form-check">
            <input 
              type="checkbox" 
              class="form-check-input" 
              id="rememberMe" 
              v-model="rememberMe"
            >
            <label class="form-check-label" for="rememberMe">记住我</label>
          </div>
          <div v-if="errorMessage" class="alert alert-danger">
            {{ errorMessage }}
          </div>
          <button 
            type="submit" 
            class="btn btn-primary w-100" 
            :disabled="loading"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>
          <div class="mt-3 text-center">
            <p>
              没有账号? 
              <router-link to="/register">注册</router-link>
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { authService } from '@/services/api';

const username = ref('');
const password = ref('');
const rememberMe = ref(false);
const errorMessage = ref('');
const loading = ref(false);
const router = useRouter();
const authStore = useAuthStore();

const handleLogin = async () => {
  loading.value = true;
  errorMessage.value = '';
  
  try {
    await authStore.login(username.value, password.value);
    router.push('/');
  } catch (error: any) {
    console.error('登录失败', error);
    errorMessage.value = error.response?.data?.message || '登录失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style> 