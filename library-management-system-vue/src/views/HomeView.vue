<template>
  <div class="home">
    <h1>图书管理系统</h1>
    <div class="row mt-4">
      <div class="col-md-3">
        <div class="card bg-primary text-white mb-4">
          <div class="card-body">
            <h5 class="card-title">总图书数</h5>
            <p class="card-text display-4">{{ stats.totalBooks || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white mb-4">
          <div class="card-body">
            <h5 class="card-title">在借图书</h5>
            <p class="card-text display-4">{{ stats.borrowedBooks || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-warning text-dark mb-4">
          <div class="card-body">
            <h5 class="card-title">逾期未还</h5>
            <p class="card-text display-4">{{ stats.overdueBooks || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-info text-white mb-4">
          <div class="card-body">
            <h5 class="card-title">用户总数</h5>
            <p class="card-text display-4">{{ stats.totalUsers || 0 }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Stats {
  totalBooks: number;
  borrowedBooks: number;
  overdueBooks: number;
  totalUsers: number;
}

const stats = ref<Stats>({
  totalBooks: 0,
  borrowedBooks: 0,
  overdueBooks: 0,
  totalUsers: 0
});

onMounted(async () => {
  // 模拟数据加载
  stats.value = {
    totalBooks: 120,
    borrowedBooks: 45,
    overdueBooks: 5,
    totalUsers: 80
  };
});
</script>

<style scoped>
.home {
  padding: 20px;
}
.display-4 {
  font-size: 2.5rem;
  font-weight: 300;
}
</style> 