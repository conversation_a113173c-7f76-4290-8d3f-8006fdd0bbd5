<template>
  <div class="borrow-management-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>借阅管理</h2>
      <button class="btn btn-primary" @click="handleNewBorrow">
        新增借阅
      </button>
    </div>

    <!-- 搜索栏 -->
    <div class="row mb-4">
      <div class="col-md-4">
        <input 
          type="text" 
          class="form-control" 
          v-model="searchQuery"
          placeholder="搜索图书或用户..."
        >
      </div>
      <div class="col-md-3">
        <select class="form-select" v-model="statusFilter">
          <option value="">所有状态</option>
          <option value="借阅中">借阅中</option>
          <option value="已归还">已归还</option>
          <option value="逾期">逾期</option>
        </select>
      </div>
    </div>

    <!-- 借阅列表 -->
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>用户</th>
            <th>图书</th>
            <th>借阅日期</th>
            <th>应还日期</th>
            <th>实际归还日期</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td colspan="8" class="text-center py-4">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
            </td>
          </tr>
          <tr v-else-if="error">
            <td colspan="8" class="text-center text-danger py-4">
              {{ error }}
            </td>
          </tr>
          <tr v-else-if="!borrows.length">
            <td colspan="8" class="text-center py-4">
              暂无借阅记录
            </td>
          </tr>
          <tr v-for="borrow in filteredBorrows" :key="borrow.id">
            <td>{{ borrow.id }}</td>
            <td>{{ borrow.userName }}</td>
            <td>{{ borrow.bookTitle }}</td>
            <td>{{ borrow.borrowDate }}</td>
            <td>{{ borrow.dueDate }}</td>
            <td>{{ borrow.returnDate || '-' }}</td>
            <td>
              <span 
                class="badge"
                :class="getBorrowStatusClass(borrow.status)"
              >
                {{ borrow.status }}
              </span>
            </td>
            <td>
              <button 
                v-if="borrow.status === '借阅中'"
                class="btn btn-sm btn-success me-2"
                @click="handleReturn(borrow.id)"
              >
                归还
              </button>
              <button 
                v-if="borrow.status === '借阅中' && borrow.renewCount < 2"
                class="btn btn-sm btn-warning me-2"
                @click="handleRenew(borrow.id)"
              >
                续借
              </button>
              <button 
                class="btn btn-sm btn-info"
                @click="handleViewDetails(borrow.id)"
              >
                详情
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- 新增借阅弹窗 -->
    <CreateBorrowModal 
      v-if="showCreateModal"
      @close="handleCloseModal"
      :onSuccess="handleCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { borrowService } from '@/services/api';
import CreateBorrowModal from '@/components/CreateBorrowModal.vue';

const showCreateModal = ref(false);
const loading = ref(false);
const error = ref('');
const borrows = ref([]);
const searchQuery = ref('');
const statusFilter = ref('');

// 获取借阅列表
const fetchBorrows = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    const response = await borrowService.getAllBorrows();
    if (response.data.success) {
      borrows.value = response.data.data;
    }
  } catch (e: any) {
    console.error('获取借阅列表失败:', e);
    error.value = e.response?.data?.message || '获取借阅列表失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

// 处理归还
const handleReturn = async (id: number) => {
  if (!confirm('确定要归还这本书吗？')) {
    return;
  }

  try {
    const response = await borrowService.updateBorrow(id, {
      status: '已归还',
      returnDate: new Date().toISOString().split('T')[0]
    });

    if (response.data.success) {
      alert('归还成功！');
      await fetchBorrows(); // 刷新列表
    }
  } catch (e: any) {
    console.error('归还图书失败:', e);
    alert(e.response?.data?.message || '归还图书失败，请稍后再试');
  }
};

// 处理续借
const handleRenew = async (id: number) => {
  if (!confirm('确定要续借这本书吗？')) {
    return;
  }

  try {
    const response = await borrowService.renewBorrow(id);
    
    if (response.data.success) {
      alert('续借成功！');
      await fetchBorrows(); // 刷新列表
    }
  } catch (e: any) {
    console.error('续借图书失败:', e);
    alert(e.response?.data?.message || '续借图书失败，请稍后再试');
  }
};

// 处理查看详情
const handleViewDetails = async (id: number) => {
  try {
    const response = await borrowService.getBorrowById(id);
    
    if (response.data.success) {
      const borrow = response.data.data;
      alert(`
        借阅详情：
        用户：${borrow.userName}
        图书：${borrow.bookTitle}
        借阅日期：${borrow.borrowDate}
        应还日期：${borrow.dueDate}
        实际归还日期：${borrow.returnDate || '未归还'}
        状态：${borrow.status}
        续借次数：${borrow.renewCount || 0}
      `);
    }
  } catch (e: any) {
    console.error('获取借阅详情失败:', e);
    alert(e.response?.data?.message || '获取借阅详情失败，请稍后再试');
  }
};

// 获取状态样式
const getBorrowStatusClass = (status: string) => {
  switch (status) {
    case '已归还':
      return 'bg-success';
    case '借阅中':
      return 'bg-primary';
    case '逾期':
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
};

// 处理新增成功
const handleCreateSuccess = () => {
  showCreateModal.value = false;
  fetchBorrows(); // 刷新借阅列表
};

// 过滤后的借阅列表
const filteredBorrows = computed(() => {
  return borrows.value.filter(borrow => {
    const matchesSearch = !searchQuery.value || 
      borrow.userName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      borrow.bookTitle.toLowerCase().includes(searchQuery.value.toLowerCase());
    
    const matchesStatus = !statusFilter.value || borrow.status === statusFilter.value;
    
    return matchesSearch && matchesStatus;
  });
});

// 处理新增借阅
const handleNewBorrow = () => {
  showCreateModal.value = true;
};

// 处理关闭弹窗
const handleCloseModal = () => {
  showCreateModal.value = false;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchBorrows();
});
</script>

<style scoped>
.borrow-management-container {
  padding: 20px;
}
</style> 