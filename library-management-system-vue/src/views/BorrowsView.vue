<template>
  <div class="borrow-container">
    <h1 class="mb-4">借阅管理</h1>
    
    <div class="d-flex justify-content-between mb-3">
      <div class="d-flex">
        <input
          type="text"
          class="form-control me-2"
          placeholder="搜索图书或用户..."
          v-model="searchQuery"
          @input="handleSearch"
        />
        <select class="form-select" v-model="statusFilter" @change="handleSearch">
          <option value="">所有状态</option>
          <option :value="BORROW_STATUS.BORROWED">借阅中</option>
          <option :value="BORROW_STATUS.RETURNED">已归还</option>
          <option :value="BORROW_STATUS.OVERDUE">逾期</option>
        </select>
      </div>
      <button class="btn btn-primary" @click="handleNewBorrow">
        新增借阅
      </button>
    </div>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>

    <div v-else-if="error" class="alert alert-danger" role="alert">
      {{ error }}
    </div>

    <div v-else class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>用户</th>
            <th>图书</th>
            <th>借阅日期</th>
            <th>应还日期</th>
            <th>实际归还日期</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="borrow in filteredBorrows" :key="borrow.id">
            <td>{{ borrow.id }}</td>
            <td>{{ borrow.userName }}</td>
            <td>{{ borrow.bookTitle }}</td>
            <td>{{ formatDateTime(borrow.borrowDate) }}</td>
            <td>{{ formatDateTime(borrow.dueDate) }}</td>
            <td>{{ borrow.returnDate ? formatDateTime(borrow.returnDate) : '未归还' }}</td>
            <td>
              <span 
                class="badge"
                :class="getBorrowStatusClass(borrow.status)"
              >
                {{ getStatusText(borrow.status) }}
              </span>
            </td>
            <td>
              <div class="btn-group">
                <button 
                  v-if="borrow.status === BORROW_STATUS.BORROWED" 
                  class="btn btn-sm btn-success me-1" 
                  @click="handleReturn(borrow.id)"
                >
                  归还
                </button>
                <button 
                  v-if="borrow.status === BORROW_STATUS.BORROWED" 
                  class="btn btn-sm btn-warning me-1" 
                  @click="handleRenew(borrow.id)"
                >
                  续借
                </button>
                <button 
                  class="btn btn-sm btn-info" 
                  @click="handleViewDetails(borrow.id)"
                >
                  详情
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <nav v-if="totalPages > 1" aria-label="Page navigation">
      <ul class="pagination justify-content-center">
        <li class="page-item" :class="{ disabled: currentPage === 1 }">
          <a class="page-link" href="#" @click.prevent="changePage(currentPage - 1)">上一页</a>
        </li>
        <li 
          v-for="page in paginationItems" 
          :key="page" 
          class="page-item" 
          :class="{ active: page === currentPage }"
        >
          <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
        </li>
        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
          <a class="page-link" href="#" @click.prevent="changePage(currentPage + 1)">下一页</a>
        </li>
      </ul>
    </nav>

    <!-- 新增借阅弹窗 -->
    <CreateBorrowModal 
      v-if="showCreateModal"
      @close="handleCloseModal"
      :onSuccess="handleCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { borrowService, userService, bookService } from '@/services/api';
import CreateBorrowModal from '@/components/CreateBorrowModal.vue';
import { BORROW_STATUS } from '@/config';

interface Borrow {
  id: number;
  userId: number;
  bookId: number;
  borrowDate: string;
  dueDate: string;
  returnDate: string | null;
  status: number;
  renewCount: number;
  remarks: string | null;
  // 扩展字段，用于显示
  userName?: string;
  bookTitle?: string;
}

const router = useRouter();
const borrows = ref<Borrow[]>([]);
const loading = ref(false);
const error = ref('');
const searchQuery = ref('');
const statusFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const showCreateModal = ref(false);

const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

const paginationItems = computed(() => {
  const items = [];
  const maxVisiblePages = 5;
  let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
  let endPage = Math.min(totalPages.value, startPage + maxVisiblePages - 1);
  
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    items.push(i);
  }
  
  return items;
});

const fetchBorrowDetails = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    // 获取借阅记录
    const borrowResponse = await borrowService.getAllBorrows();
    if (!borrowResponse.data.success) {
      error.value = borrowResponse.data.message || '获取借阅记录失败';
      return;
    }

    const borrowRecords = borrowResponse.data.data;
    const enrichedBorrows = [];

    // 为每条借阅记录获取用户和图书信息
    for (const borrow of borrowRecords) {
      try {
        // 获取用户信息
        const userResponse = await userService.getUserById(borrow.userId);
        const user = userResponse.data.success ? userResponse.data.data : null;

        // 获取图书信息
        const bookResponse = await bookService.getBookById(borrow.bookId);
        const book = bookResponse.data.success ? bookResponse.data.data : null;

        enrichedBorrows.push({
          ...borrow,
          userName: user?.name || `用户${borrow.userId}`,
          bookTitle: book?.title || `图书${borrow.bookId}`
        });
      } catch (e) {
        console.error('获取详情失败:', e);
        // 即使获取详情失败，也添加基本信息
        enrichedBorrows.push({
          ...borrow,
          userName: `用户${borrow.userId}`,
          bookTitle: `图书${borrow.bookId}`
        });
      }
    }

    borrows.value = enrichedBorrows;
    totalItems.value = enrichedBorrows.length;
  } catch (e: any) {
    console.error('获取借阅列表失败:', e);
    error.value = e.response?.data?.message || '获取借阅列表失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const fetchBorrows = fetchBorrowDetails;

const handleSearch = () => {
  currentPage.value = 1;
  fetchBorrows();
};

const changePage = (page: number) => {
  if (page < 1 || page > totalPages.value) return;
  currentPage.value = page;
  fetchBorrows();
};

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case BORROW_STATUS.BORROWED:
      return '借阅中';
    case BORROW_STATUS.RETURNED:
      return '已归还';
    case BORROW_STATUS.OVERDUE:
      return '逾期';
    default:
      return '未知状态';
  }
};

// 获取状态样式
const getBorrowStatusClass = (status: number) => {
  switch (status) {
    case BORROW_STATUS.RETURNED:
      return 'bg-success';
    case BORROW_STATUS.BORROWED:
      return 'bg-primary';
    case BORROW_STATUS.OVERDUE:
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
};

const handleReturn = async (id: number) => {
  if (!confirm('确定要归还这本书吗？')) {
    return;
  }

  try {
    const response = await borrowService.updateBorrow(id, {
      status: BORROW_STATUS.RETURNED,
      returnDate: new Date().toISOString().split('T')[0]
    });

    if (response.data.success) {
      alert('归还成功！');
      await fetchBorrows(); // 刷新列表
    }
  } catch (e: any) {
    console.error('归还图书失败:', e);
    alert(e.response?.data?.message || '归还图书失败，请稍后再试');
  }
};

const handleRenew = async (id: number) => {
  if (!confirm('确定要续借这本书吗？')) {
    return;
  }

  try {
    const response = await borrowService.renewBorrow(id);
    
    if (response.data.success) {
      alert('续借成功！');
      await fetchBorrows(); // 刷新列表
    }
  } catch (e: any) {
    console.error('续借图书失败:', e);
    alert(e.response?.data?.message || '续借图书失败，请稍后再试');
  }
};

const handleViewDetails = async (id: number) => {
  try {
    const response = await borrowService.getBorrowById(id);
    
    if (response.data.success) {
      const borrow = response.data.data;
      alert(`
        借阅详情：
        用户：${borrow.userName}
        图书：${borrow.bookTitle}
        借阅日期：${formatDateTime(borrow.borrowDate)}
        应还日期：${formatDateTime(borrow.dueDate)}
        实际归还日期：${borrow.returnDate ? formatDateTime(borrow.returnDate) : '未归还'}
        状态：${getStatusText(borrow.status)}
        续借次数：${borrow.renewCount || 0}
      `);
    }
  } catch (e: any) {
    console.error('获取借阅详情失败:', e);
    alert(e.response?.data?.message || '获取借阅详情失败，请稍后再试');
  }
};

const handleNewBorrow = () => {
  showCreateModal.value = true;
};

// 处理关闭弹窗
const handleCloseModal = () => {
  showCreateModal.value = false;
};

// 处理新增成功
const handleCreateSuccess = () => {
  showCreateModal.value = false;
  fetchBorrows(); // 刷新列表
};

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return '';
  return dateTimeStr.replace('T', ' ').split('.')[0];
};

// 过滤后的借阅列表
const filteredBorrows = computed(() => {
  return borrows.value.filter(borrow => {
    const matchesSearch = !searchQuery.value || 
      borrow.userName?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      borrow.bookTitle?.toLowerCase().includes(searchQuery.value.toLowerCase());
    
    const matchesStatus = !statusFilter.value || borrow.status === Number(statusFilter.value);
    
    return matchesSearch && matchesStatus;
  });
});

onMounted(() => {
  fetchBorrows();
});
</script>

<style scoped>
.borrow-container {
  padding: 20px;
}
</style> 