<template>
  <div class="book-detail-container">
    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>
    
    <div v-else-if="error" class="alert alert-danger" role="alert">
      {{ error }}
      <div class="mt-3">
        <router-link to="/books" class="btn btn-outline-primary">
          返回图书列表
        </router-link>
      </div>
    </div>
    
    <div v-else-if="book" class="row">
      <div class="col-md-4 mb-4 mb-md-0">
        <div class="card">
          <img :src="book.coverUrl || '/images/default-book.jpg'" class="card-img-top book-cover" :alt="book.title">
          <div class="card-body text-center">
            <div class="mb-3">
              <span class="badge bg-primary me-2">{{ book.category }}</span>
              <span class="badge" :class="book.status === 1 ? 'bg-success' : 'bg-secondary'">
                {{ book.status === 1 ? '可借阅' : '不可借阅' }}
              </span>
              <span v-if="book.availableCopies > 0" class="badge bg-info ms-2">
                库存: {{ book.availableCopies }} 本
              </span>
              <span v-else class="badge bg-danger ms-2">
                已无库存
              </span>
            </div>
            <div class="d-grid">
              <button 
                v-if="book.status === 1 && book.availableCopies > 0" 
                class="btn btn-success" 
                @click="handleBorrow"
                :disabled="borrowing"
              >
                <span v-if="borrowing" class="spinner-border spinner-border-sm me-1" role="status"></span>
                借阅这本书
              </button>
              <button v-else-if="book.status !== 1" class="btn btn-secondary" disabled>
                暂不可借阅
              </button>
              <button v-else class="btn btn-secondary" disabled>
                已无库存
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-8">
        <nav aria-label="breadcrumb" class="mb-3">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><router-link to="/books">图书列表</router-link></li>
            <li class="breadcrumb-item active" aria-current="page">图书详情</li>
          </ol>
        </nav>
        
        <div class="card mb-4">
          <div class="card-body">
            <h1 class="book-title mb-3">{{ book.title }}</h1>
            <div class="book-meta mb-4">
              <p><strong>作者:</strong> {{ book.author }}</p>
              <p><strong>出版社:</strong> {{ book.publisher }}</p>
              <p><strong>出版日期:</strong> {{ book.publishDate }}</p>
              <p><strong>ISBN:</strong> {{ book.isbn }}</p>
              <p><strong>分类:</strong> {{ book.category }}</p>
              <p><strong>总数量:</strong> {{ book.totalCopies }} 本</p>
              <p><strong>可借数量:</strong> {{ book.availableCopies }} 本</p>
            </div>
            
            <h4 class="mb-3">简介</h4>
            <div class="book-description mb-4">
              <p>{{ book.description || '暂无简介' }}</p>
            </div>
            
            <h4 class="mb-3">目录</h4>
            <div class="book-toc">
              <p v-if="!book.toc">暂无目录信息</p>
              <div v-else v-html="formatToc(book.toc)"></div>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">读者评价</h5>
            <button class="btn btn-sm btn-primary" @click="showReviewModal = true">
              写评价
            </button>
          </div>
          <div class="card-body">
            <div v-if="loadingReviews" class="text-center my-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
            </div>
            
            <div v-else-if="reviewError" class="alert alert-danger" role="alert">
              {{ reviewError }}
            </div>
            
            <div v-else-if="reviews.length === 0" class="text-center my-4">
              <p class="text-muted">暂无评价</p>
              <button class="btn btn-outline-primary" @click="showReviewModal = true">
                成为第一个评价的读者
              </button>
            </div>
            
            <div v-else>
              <div v-for="review in reviews" :key="review.id" class="review-item">
                <div class="d-flex justify-content-between mb-2">
                  <div>
                    <span class="fw-bold">{{ review.userName }}</span>
                    <span class="ms-2 text-muted small">{{ review.createdAt }}</span>
                  </div>
                  <div class="stars">
                    <i 
                      v-for="n in 5" 
                      :key="n" 
                      class="bi" 
                      :class="n <= review.rating ? 'bi-star-fill text-warning' : 'bi-star'"
                    ></i>
                  </div>
                </div>
                <p>{{ review.comment }}</p>
                <hr>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 写评价弹窗 -->
    <a-modal
      v-model:visible="showReviewModal"
      title="写评价"
      @ok="submitReview"
      :confirmLoading="submittingReview"
      :maskClosable="false"
    >
      <div class="mb-3">
        <label class="form-label">评分</label>
        <div class="rating-stars">
          <i 
            v-for="n in 5" 
            :key="n" 
            class="bi me-1" 
            :class="n <= reviewForm.rating ? 'bi-star-fill text-warning' : 'bi-star'"
            @click="reviewForm.rating = n"
            style="cursor: pointer;"
          ></i>
        </div>
      </div>
      <div class="mb-3">
        <label class="form-label">评价内容</label>
        <a-textarea
          v-model:value="reviewForm.content"
          :rows="4"
          placeholder="分享你的阅读体验..."
        />
      </div>
      <div v-if="reviewSubmitError" class="alert alert-danger mt-3">
        {{ reviewSubmitError }}
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { bookService, reviewService } from '@/services/api';
import dayjs from 'dayjs';

const route = useRoute();
const bookId = Number(route.params.id);

const book = ref<any>(null);
const loading = ref(false);
const error = ref('');

const reviews = ref<any[]>([]);
const loadingReviews = ref(false);
const reviewError = ref('');
const showReviewModal = ref(false);
const submittingReview = ref(false);
const reviewSubmitError = ref('');
const borrowing = ref(false);

const reviewForm = ref({
  rating: 0,
  content: ''
});

const fetchBook = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    const response = await bookService.getBookById(bookId);
    if (response.data.success) {
      book.value = response.data.data;
    } else {
      error.value = response.data.message || '获取图书信息失败';
    }
  } catch (e: any) {
    console.error('获取图书信息失败:', e);
    error.value = e.response?.data?.message || '获取图书信息失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const fetchReviews = async () => {
  loadingReviews.value = true;
  reviewError.value = '';
  
  try {
    const response = await reviewService.getBookReviews(bookId);
    console.log('评论数据:', response.data);
    if (response.data.success) {
      reviews.value = response.data.data.map((review: any) => ({
        ...review,
        createdAt: dayjs(review.createdTime).format('YYYY-MM-DD HH:mm')
      }));
    } else {
      reviewError.value = response.data.message || '获取评论失败';
    }
  } catch (e: any) {
    console.error('获取评论失败:', e);
    reviewError.value = e.response?.data?.message || '获取评论失败，请稍后再试';
  } finally {
    loadingReviews.value = false;
  }
};

const handleBorrow = async () => {
  if (!book.value) return;
  
  borrowing.value = true;
  try {
    const response = await bookService.borrowBook(bookId);
    if (response.data.success) {
      message.success('借阅成功！');
      await fetchBook(); // 刷新图书信息
    } else {
      message.error(response.data.message || '借阅失败');
    }
  } catch (e: any) {
    console.error('借阅失败:', e);
    message.error(e.response?.data?.message || '借阅失败，请稍后再试');
  } finally {
    borrowing.value = false;
  }
};

const submitReview = async () => {
  if (!reviewForm.value.rating) {
    message.error('请选择评分');
    return;
  }
  if (!reviewForm.value.content.trim()) {
    message.error('请填写评价内容');
    return;
  }

  submittingReview.value = true;
  reviewSubmitError.value = '';

  try {
    const response = await reviewService.addReview(bookId, {
      rating: reviewForm.value.rating,
      comment: reviewForm.value.content.trim()
    });

    if (response.data.success) {
      message.success('评价提交成功');
      showReviewModal.value = false;
      // 重置表单
      reviewForm.value = {
        rating: 0,
        content: ''
      };
      // 重新获取评论列表
      await fetchReviews();
    } else {
      reviewSubmitError.value = response.data.message || '提交评价失败';
    }
  } catch (e: any) {
    console.error('提交评价失败:', e);
    reviewSubmitError.value = e.response?.data?.message || '提交评价失败，请稍后再试';
  } finally {
    submittingReview.value = false;
  }
};

onMounted(() => {
  fetchBook();
  fetchReviews();
});
</script>

<style scoped>
.book-detail-container {
  padding: 20px;
}

.book-cover {
  max-height: 400px;
  object-fit: contain;
}

.book-title {
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.rating-stars {
  font-size: 1.5rem;
  color: #d4d4d4;
}

.rating-stars .bi-star-fill {
  color: #ffc107;
}

.review-item {
  margin-bottom: 1.5rem;
}

.review-item:last-child hr {
  display: none;
}

.stars {
  color: #ffc107;
}
</style> 