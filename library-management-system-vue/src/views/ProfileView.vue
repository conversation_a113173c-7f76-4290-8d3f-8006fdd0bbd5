<template>
  <div class="profile-container">
    <div class="row">
      <div class="col-md-4 mb-4 mb-md-0">
        <div class="card">
          <div class="card-body">
            <div class="text-center mb-4">
              <div class="avatar-placeholder mb-3">
                <span v-if="user">{{ user.name.substring(0, 1) }}</span>
                <div v-else class="spinner-border spinner-border-sm"></div>
              </div>
              <h3 v-if="user">{{ user.name }}</h3>
              <p v-if="user" class="text-muted">
                {{ user.role === 1 ? '管理员' : '读者' }}
              </p>
            </div>

            <ul class="list-group list-group-flush">
              <li class="list-group-item d-flex justify-content-between align-items-center">
                用户名
                <span v-if="user">{{ user.username }}</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                邮箱
                <span v-if="user">{{ user.email }}</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                状态
                <span v-if="user" class="badge bg-success">{{ user.status === 1 ? '正常' : '禁用' }}</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                注册时间
                <span v-if="user">{{ user.createdAt }}</span>
              </li>
            </ul>
          </div>
          <div class="card-footer">
            <button class="btn btn-primary w-100" @click="handleEdit">
              编辑个人信息
            </button>
          </div>
        </div>
      </div>
      
      <div class="col-md-8">
        <div class="card mb-4">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">我的借阅</h5>
            <router-link to="/borrows" class="btn btn-sm btn-outline-primary">
              查看全部
            </router-link>
          </div>
          <div class="card-body">
            <div v-if="loadingBorrows" class="text-center my-5">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
            </div>
            
            <div v-else-if="borrowError" class="alert alert-danger" role="alert">
              {{ borrowError }}
            </div>
            
            <div v-else-if="borrows.length === 0" class="text-center my-4">
              <p class="text-muted">暂无借阅记录</p>
              <router-link to="/books" class="btn btn-primary">
                去借阅图书
              </router-link>
            </div>
            
            <div v-else>
              <div v-for="borrow in borrows" :key="borrow.id" class="card mb-3 borrow-card">
                <div class="card-body">
                  <div class="d-flex align-items-center mb-2">
                    <h5 class="mb-0">{{ borrow.bookTitle }}</h5>
                    <span 
                      class="ms-auto badge"
                      :class="getBorrowStatusClass(borrow.status)"
                    >
                      {{ borrow.status }}
                    </span>
                  </div>
                  <div class="mb-2 text-muted small">
                    <span>借阅日期: {{ borrow.borrowDate }}</span>
                    <span class="mx-2">•</span>
                    <span>应还日期: {{ borrow.dueDate }}</span>
                    <span v-if="isOverdue(borrow.dueDate)" class="text-danger ms-2">
                      (已逾期)
                    </span>
                  </div>
                  <div class="mb-2 text-muted small">
                    <span>续借次数: {{ borrow.renewCount }}/2</span>
                    <span class="mx-2">•</span>
                    <span>借阅时长: {{ borrow.borrowDuration }}天</span>
                  </div>
                  <div v-if="borrow.status === '借阅中'" class="d-flex justify-content-end">
                    <button 
                      class="btn btn-sm btn-success me-2" 
                      @click="handleReturn(borrow.id)"
                    >
                      归还
                    </button>
                    <button 
                      v-if="borrow.renewCount < 2" 
                      class="btn btn-sm btn-warning" 
                      @click="handleRenew(borrow.id)"
                    >
                      续借
                    </button>
                    <small v-else class="text-muted align-self-center">
                      已达最大续借次数
                    </small>
                  </div>
                </div>
              </div>
              
              <!-- 分页 -->
              <nav v-if="totalBorrows > pageSize" aria-label="借阅记录分页">
                <ul class="pagination justify-content-center">
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <a class="page-link" href="#" @click.prevent="currentPage--; fetchUserBorrows()">上一页</a>
                  </li>
                  <li class="page-item" :class="{ active: currentPage === page }" v-for="page in Math.ceil(totalBorrows / pageSize)" :key="page">
                    <a class="page-link" href="#" @click.prevent="currentPage = page; fetchUserBorrows()">{{ page }}</a>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === Math.ceil(totalBorrows / pageSize) }">
                    <a class="page-link" href="#" @click.prevent="currentPage++; fetchUserBorrows()">下一页</a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">修改密码</h5>
          </div>
          <div class="card-body">
            <form @submit.prevent="handleChangePassword">
              <div class="mb-3">
                <label for="currentPassword" class="form-label">当前密码</label>
                <input 
                  type="password" 
                  class="form-control" 
                  id="currentPassword"
                  v-model="passwordForm.currentPassword"
                  required
                >
              </div>
              <div class="mb-3">
                <label for="newPassword" class="form-label">新密码</label>
                <input 
                  type="password" 
                  class="form-control" 
                  id="newPassword"
                  v-model="passwordForm.newPassword"
                  required
                >
              </div>
              <div class="mb-3">
                <label for="confirmPassword" class="form-label">确认新密码</label>
                <input 
                  type="password" 
                  class="form-control" 
                  id="confirmPassword"
                  v-model="passwordForm.confirmPassword"
                  required
                >
                <div 
                  v-if="passwordForm.newPassword && passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword" 
                  class="text-danger mt-1"
                >
                  两次输入的密码不一致
                </div>
              </div>
              <div v-if="passwordChangeSuccess" class="alert alert-success" role="alert">
                密码修改成功！
              </div>
              <div v-if="passwordChangeError" class="alert alert-danger" role="alert">
                {{ passwordChangeError }}
              </div>
              <button 
                type="submit" 
                class="btn btn-primary"
                :disabled="isPasswordFormInvalid || passwordChanging"
              >
                <span v-if="passwordChanging" class="spinner-border spinner-border-sm me-1" role="status"></span>
                修改密码
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 编辑个人信息弹窗 -->
    <div v-if="showEditModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">编辑个人信息</h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showEditModal = false"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitEdit">
              <div class="mb-3">
                <label for="name" class="form-label">姓名</label>
                <input 
                  type="text" 
                  class="form-control" 
                  id="name"
                  v-model="editForm.name"
                  required
                >
              </div>
              <div class="mb-3">
                <label for="email" class="form-label">邮箱</label>
                <input 
                  type="email" 
                  class="form-control" 
                  id="email"
                  v-model="editForm.email"
                  required
                >
              </div>
              <div v-if="editError" class="alert alert-danger" role="alert">
                {{ editError }}
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="showEditModal = false"
            >
              取消
            </button>
            <button 
              type="button" 
              class="btn btn-primary" 
              @click="submitEdit"
              :disabled="!editForm.name.trim() || !editForm.email.trim() || editing"
            >
              <span v-if="editing" class="spinner-border spinner-border-sm me-1" role="status"></span>
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { userService, borrowService } from '@/services/api';

interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  role: number;
  status: number;
  createdAt: string;
}

interface Borrow {
  id: number;
  bookId: number;
  bookTitle: string;
  borrowDate: string;
  dueDate: string;
  returnDate: string | null;
  status: string;
  renewCount: number;
}

const user = ref<User | null>(null);
const borrows = ref<Borrow[]>([]);
const loading = ref(false);
const loadingBorrows = ref(false);
const borrowError = ref('');

const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});
const passwordChanging = ref(false);
const passwordChangeSuccess = ref(false);
const passwordChangeError = ref('');

const showEditModal = ref(false);
const editForm = ref({
  name: '',
  email: ''
});
const editing = ref(false);
const editError = ref('');

const currentPage = ref(1);
const pageSize = ref(5);
const totalBorrows = ref(0);

const isPasswordFormInvalid = computed(() => {
  return !passwordForm.value.currentPassword ||
    !passwordForm.value.newPassword ||
    !passwordForm.value.confirmPassword ||
    passwordForm.value.newPassword !== passwordForm.value.confirmPassword;
});

const fetchUserProfile = async () => {
  loading.value = true;
  
  try {
    const response = await userService.getCurrentUser();
    if (response.data.success) {
      user.value = response.data.data;
    }
  } catch (e) {
    console.error(e);
  } finally {
    loading.value = false;
  }
};

const fetchUserBorrows = async () => {
  loadingBorrows.value = true;
  borrowError.value = '';
  
  try {
    const response = await borrowService.getUserBorrows({
      page: currentPage.value,
      pageSize: pageSize.value
    });
    if (response.data.success) {
      borrows.value = response.data.data;
      totalBorrows.value = response.data.total;
    }
  } catch (e) {
    console.error(e);
    borrowError.value = '获取借阅记录失败，请稍后再试';
  } finally {
    loadingBorrows.value = false;
  }
};

const getBorrowStatusClass = (status: string) => {
  switch (status) {
    case '已归还':
      return 'bg-success';
    case '借阅中':
      return 'bg-primary';
    case '逾期':
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
};

const isOverdue = (dueDate: string) => {
  return new Date(dueDate) < new Date();
};

const handleEdit = () => {
  if (user.value) {
    editForm.value = {
      name: user.value.name,
      email: user.value.email
    };
    showEditModal.value = true;
  }
};

const submitEdit = async () => {
  if (!editForm.value.name.trim() || !editForm.value.email.trim()) {
    editError.value = '请填写所有必填字段';
    return;
  }
  
  editing.value = true;
  editError.value = '';
  
  try {
    const response = await userService.updateUser(user.value!.id, {
      ...user.value,
      name: editForm.value.name,
      email: editForm.value.email
    });
    
    if (response.data.success) {
      user.value = response.data.data;
      showEditModal.value = false;
    }
  } catch (e: any) {
    console.error(e);
    editError.value = e.response?.data?.message || '更新个人信息失败，请稍后再试';
  } finally {
    editing.value = false;
  }
};

const handleReturn = async (id: number) => {
  try {
    const response = await borrowService.returnBook(id);
    if (response.data.success) {
      await fetchUserBorrows(); // 刷新借阅列表
    }
  } catch (e) {
    console.error('归还图书失败:', e);
  }
};

const handleRenew = async (id: number) => {
  try {
    const response = await borrowService.renewBook(id);
    if (response.data.success) {
      await fetchUserBorrows(); // 刷新借阅列表
    }
  } catch (e) {
    console.error('续借图书失败:', e);
  }
};

const handleChangePassword = async () => {
  if (isPasswordFormInvalid.value) return;
  
  passwordChanging.value = true;
  passwordChangeSuccess.value = false;
  passwordChangeError.value = '';
  
  try {
    const response = await userService.changePassword({
      currentPassword: passwordForm.value.currentPassword,
      newPassword: passwordForm.value.newPassword
    });
    
    if (response.data.success) {
      passwordChangeSuccess.value = true;
      
      // 清空表单
      passwordForm.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    }
  } catch (e: any) {
    console.error(e);
    passwordChangeError.value = e.response?.data?.message || '修改密码失败，请稍后再试';
  } finally {
    passwordChanging.value = false;
  }
};

onMounted(() => {
  fetchUserProfile();
  fetchUserBorrows();
});
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  margin: 0 auto;
}

.borrow-card {
  transition: all 0.2s ease;
}

.borrow-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}
</style> 