<template>
  <div class="book-management-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1>图书管理</h1>
      <button class="btn btn-primary" @click="showAddBookModal = true">
        添加新图书
      </button>
    </div>
    
    <div class="card mb-4">
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-search"></i>
              </span>
              <input 
                type="text" 
                class="form-control" 
                placeholder="输入关键词搜索" 
                v-model="searchQuery"
                @input="handleSearch"
              >
            </div>
          </div>
          <div class="col-md-3">
            <select class="form-select" v-model="categoryFilter" @change="handleFilter">
              <option value="">所有分类</option>
              <option v-for="category in categories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
          </div>
          <div class="col-md-3">
            <select class="form-select" v-model="statusFilter" @change="handleFilter">
              <option value="">所有状态</option>
              <option value="1">可借阅</option>
              <option value="0">不可借阅</option>
            </select>
          </div>
          <div class="col-md-3">
            <button class="btn btn-outline-secondary w-100" @click="resetFilters">
              重置筛选
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
    </div>
    
    <div v-else-if="error" class="alert alert-danger" role="alert">
      {{ error }}
    </div>
    
    <div v-else-if="filteredBooks.length === 0" class="text-center my-5">
      <p class="text-muted">未找到符合条件的图书</p>
      <button class="btn btn-outline-primary mt-3" @click="resetFilters">
        清除筛选条件
      </button>
    </div>
    
    <div v-else>
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th style="width: 80px">图书ID</th>
              <th style="width: 250px">标题</th>
              <th>作者</th>
              <th>分类</th>
              <th>出版社</th>
              <th>库存</th>
              <th>可借阅</th>
              <th>状态</th>
              <th style="width: 150px">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="book in paginatedBooks" :key="book.id">
              <td>{{ book.id }}</td>
              <td>
                <div class="d-flex align-items-center">
                  <img 
                    :src="book.coverUrl || '/images/default-book.jpg'" 
                    :alt="book.title" 
                    class="book-thumbnail me-2"
                  >
                  <div>
                    <router-link :to="`/books/${book.id}`" class="book-title-link">
                      {{ book.title }}
                    </router-link>
                    <div class="small text-muted">ISBN: {{ book.isbn }}</div>
                  </div>
                </div>
              </td>
              <td>{{ book.author }}</td>
              <td>{{ book.category }}</td>
              <td>{{ book.publisher }}</td>
              <td>{{ book.stock }}</td>
              <td>{{ book.available }}</td>
              <td>
                <span 
                  class="badge" 
                  :class="book.status === 1 ? 'bg-success' : 'bg-secondary'"
                >
                  {{ book.status === 1 ? '可借阅' : '不可借阅' }}
                </span>
              </td>
              <td>
                <div class="btn-group">
                  <button 
                    class="btn btn-sm btn-outline-primary" 
                    @click="editBook(book)"
                    title="编辑"
                  >
                    <i class="bi bi-pencil"></i>
                  </button>
                  <button 
                    class="btn btn-sm btn-outline-danger" 
                    @click="confirmDelete(book)"
                    title="删除"
                  >
                    <i class="bi bi-trash"></i>
                  </button>
                  <button 
                    class="btn btn-sm btn-outline-success" 
                    @click="toggleStatus(book)"
                    title="切换状态"
                  >
                    <i class="bi" :class="book.status === 1 ? 'bi-toggle-on' : 'bi-toggle-off'"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页控件 -->
      <div class="d-flex justify-content-between align-items-center mt-4">
        <div>
          显示 {{ startItem }} - {{ endItem }} 项，共 {{ filteredBooks.length }} 项
        </div>
        <nav aria-label="Page navigation">
          <ul class="pagination">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <button class="page-link" @click="currentPage = 1">首页</button>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <button class="page-link" @click="currentPage--">上一页</button>
            </li>
            <li 
              v-for="page in displayedPages" 
              :key="page" 
              class="page-item"
              :class="{ active: currentPage === page }"
            >
              <button class="page-link" @click="currentPage = page">{{ page }}</button>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <button class="page-link" @click="currentPage++">下一页</button>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <button class="page-link" @click="currentPage = totalPages">末页</button>
            </li>
          </ul>
        </nav>
      </div>
    </div>
    
    <!-- 添加/编辑图书弹窗 -->
    <div v-if="showAddBookModal || showEditBookModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ showEditBookModal ? '编辑图书' : '添加新图书' }}</h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="closeBookModal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitBookForm">
              <div class="row g-3">
                <div class="col-md-6">
                  <label for="bookTitle" class="form-label">图书标题 <span class="text-danger">*</span></label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="bookTitle" 
                    v-model="bookForm.title" 
                    required
                  >
                </div>
                <div class="col-md-6">
                  <label for="bookAuthor" class="form-label">作者 <span class="text-danger">*</span></label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="bookAuthor" 
                    v-model="bookForm.author" 
                    required
                  >
                </div>
                <div class="col-md-6">
                  <label for="bookIsbn" class="form-label">ISBN <span class="text-danger">*</span></label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="bookIsbn" 
                    v-model="bookForm.isbn" 
                    required
                  >
                </div>
                <div class="col-md-6">
                  <label for="bookCategory" class="form-label">分类 <span class="text-danger">*</span></label>
                  <select 
                    class="form-select" 
                    id="bookCategory" 
                    v-model="bookForm.category" 
                    required
                  >
                    <option value="">选择分类</option>
                    <option v-for="category in categories" :key="category" :value="category">
                      {{ category }}
                    </option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label for="bookPublisher" class="form-label">出版社</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="bookPublisher" 
                    v-model="bookForm.publisher"
                  >
                </div>
                <div class="col-md-6">
                  <label for="bookPublicationDate" class="form-label">出版日期</label>
                  <input 
                    type="date" 
                    class="form-control" 
                    id="bookPublicationDate" 
                    v-model="bookForm.publicationDate"
                  >
                </div>
                <div class="col-md-4">
                  <label for="bookLanguage" class="form-label">语言</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="bookLanguage" 
                    v-model="bookForm.language"
                  >
                </div>
                <div class="col-md-4">
                  <label for="bookPages" class="form-label">页数</label>
                  <input 
                    type="number" 
                    class="form-control" 
                    id="bookPages" 
                    v-model="bookForm.pages"
                    min="1"
                  >
                </div>
                <div class="col-md-4">
                  <label for="bookStock" class="form-label">库存 <span class="text-danger">*</span></label>
                  <input 
                    type="number" 
                    class="form-control" 
                    id="bookStock" 
                    v-model="bookForm.stock"
                    min="0" 
                    required
                  >
                </div>
                <div class="col-12">
                  <label for="bookCoverUrl" class="form-label">封面图片URL</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="bookCoverUrl" 
                    v-model="bookForm.coverUrl"
                    placeholder="http://example.com/image.jpg"
                  >
                </div>
                <div class="col-12">
                  <label for="bookDescription" class="form-label">图书简介</label>
                  <textarea 
                    class="form-control" 
                    id="bookDescription" 
                    v-model="bookForm.description" 
                    rows="3"
                  ></textarea>
                </div>
                <div class="col-12">
                  <label for="bookToc" class="form-label">目录</label>
                  <textarea 
                    class="form-control" 
                    id="bookToc" 
                    v-model="bookForm.toc" 
                    rows="3"
                    placeholder="第一章 xxx&#10;第二章 xxx"
                  ></textarea>
                </div>
                <div class="col-12">
                  <div class="form-check">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      id="bookStatus" 
                      v-model="bookForm.status"
                    >
                    <label class="form-check-label" for="bookStatus">
                      可借阅
                    </label>
                  </div>
                </div>
              </div>
              
              <div v-if="bookFormError" class="alert alert-danger mt-3" role="alert">
                {{ bookFormError }}
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeBookModal">
              取消
            </button>
            <button 
              type="button" 
              class="btn btn-primary" 
              @click="submitBookForm"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
              {{ showEditBookModal ? '保存修改' : '添加图书' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">确认删除</h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showDeleteModal = false"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <p>确定要删除图书 <strong>{{ bookToDelete?.title }}</strong> 吗？此操作无法撤销。</p>
            <div v-if="deleteError" class="alert alert-danger mt-3" role="alert">
              {{ deleteError }}
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="showDeleteModal = false">
              取消
            </button>
            <button 
              type="button" 
              class="btn btn-danger" 
              @click="deleteBook"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="spinner-border spinner-border-sm me-1" role="status"></span>
              确认删除
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 操作成功提示 -->
    <div v-if="successMessage" class="alert alert-success fixed-alert" role="alert">
      {{ successMessage }}
      <button type="button" class="btn-close float-end" @click="successMessage = ''"></button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';

interface Book {
  id: number;
  title: string;
  author: string;
  publisher: string;
  publicationDate: string;
  isbn: string;
  language: string;
  pages: number;
  description: string;
  coverUrl: string | null;
  category: string;
  stock: number;
  available: number;
  status: number;
  toc: string | null;
}

// 状态变量
const books = ref<Book[]>([]);
const loading = ref(true);
const error = ref('');

// 筛选和分页变量
const searchQuery = ref('');
const categoryFilter = ref('');
const statusFilter = ref('');
const currentPage = ref(1);
const pageSize = 10;

// 弹窗状态
const showAddBookModal = ref(false);
const showEditBookModal = ref(false);
const showDeleteModal = ref(false);
const bookToDelete = ref<Book | null>(null);
const successMessage = ref('');

// 表单状态
const bookForm = ref({
  id: 0,
  title: '',
  author: '',
  publisher: '',
  publicationDate: '',
  isbn: '',
  language: '',
  pages: 0,
  description: '',
  coverUrl: '',
  category: '',
  stock: 0,
  available: 0,
  status: true,
  toc: ''
});
const bookFormError = ref('');
const isSubmitting = ref(false);
const isDeleting = ref(false);
const deleteError = ref('');

// 分类列表
const categories = ref([
  '科幻小说',
  '文学小说',
  '历史',
  '哲学',
  '科普',
  '计算机',
  '经济管理',
  '艺术设计',
  '教育',
  '医学健康'
]);

// 模拟数据
const mockBooks: Book[] = [
  {
    id: 1,
    title: '三体',
    author: '刘慈欣',
    publisher: '重庆出版社',
    publicationDate: '2008-01-01',
    isbn: '9787536692930',
    language: '中文',
    pages: 302,
    description: '《三体》是刘慈欣创作的长篇科幻小说，是"地球往事三部曲"系列的第一部。',
    coverUrl: '/images/default-book.jpg',
    category: '科幻小说',
    stock: 10,
    available: 7,
    status: 1,
    toc: '第一部 无声的地球\n第一章 科学边界'
  },
  {
    id: 2,
    title: '平凡的世界',
    author: '路遥',
    publisher: '人民文学出版社',
    publicationDate: '1986-01-01',
    isbn: '9787020049295',
    language: '中文',
    pages: 1130,
    description: '《平凡的世界》是中国作家路遥创作的一部百万字的小说，全景式地展示了中国当代城乡的社会生活。',
    coverUrl: '/images/default-book.jpg',
    category: '文学小说',
    stock: 8,
    available: 5,
    status: 1,
    toc: '第一卷\n第一章'
  },
  {
    id: 3,
    title: '人类简史',
    author: '尤瓦尔·赫拉利',
    publisher: '中信出版社',
    publicationDate: '2014-11-01',
    isbn: '9787508647357',
    language: '中文',
    pages: 440,
    description: '《人类简史：从动物到上帝》是以色列历史学家尤瓦尔·赫拉利创作的科普著作。',
    coverUrl: '/images/default-book.jpg',
    category: '历史',
    stock: 12,
    available: 12,
    status: 1,
    toc: '第一章 人类：一种没有特别重要的动物'
  },
  {
    id: 4,
    title: '浪潮之巅',
    author: '吴军',
    publisher: '电子工业出版社',
    publicationDate: '2011-08-01',
    isbn: '9787121139512',
    language: '中文',
    pages: 524,
    description: '《浪潮之巅》以IT产业发展为主线，讲述了IT产业发展的历史及其规律。',
    coverUrl: '/images/default-book.jpg',
    category: '计算机',
    stock: 5,
    available: 0,
    status: 0,
    toc: '前言\n第一章 帝国的余辉'
  }
];

// 计算属性
const filteredBooks = computed(() => {
  let result = books.value;
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(book => 
      book.title.toLowerCase().includes(query) || 
      book.author.toLowerCase().includes(query) || 
      book.isbn.toLowerCase().includes(query)
    );
  }
  
  // 分类过滤
  if (categoryFilter.value) {
    result = result.filter(book => book.category === categoryFilter.value);
  }
  
  // 状态过滤
  if (statusFilter.value !== '') {
    const status = parseInt(statusFilter.value);
    result = result.filter(book => book.status === status);
  }
  
  return result;
});

const totalPages = computed(() => {
  return Math.ceil(filteredBooks.value.length / pageSize);
});

const paginatedBooks = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return filteredBooks.value.slice(startIndex, endIndex);
});

const startItem = computed(() => {
  if (filteredBooks.value.length === 0) return 0;
  return (currentPage.value - 1) * pageSize + 1;
});

const endItem = computed(() => {
  if (filteredBooks.value.length === 0) return 0;
  const end = currentPage.value * pageSize;
  return end > filteredBooks.value.length ? filteredBooks.value.length : end;
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  let startPage = Math.max(1, currentPage.value - Math.floor(maxPagesToShow / 2));
  let endPage = Math.min(totalPages.value, startPage + maxPagesToShow - 1);
  
  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  
  return pages;
});

// 方法
const fetchBooks = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟成功响应
    books.value = mockBooks;
  } catch (e) {
    console.error(e);
    error.value = '获取图书列表失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handleFilter = () => {
  currentPage.value = 1;
};

const resetFilters = () => {
  searchQuery.value = '';
  categoryFilter.value = '';
  statusFilter.value = '';
  currentPage.value = 1;
};

const editBook = (book: Book) => {
  bookForm.value = {
    id: book.id,
    title: book.title,
    author: book.author,
    publisher: book.publisher || '',
    publicationDate: book.publicationDate || '',
    isbn: book.isbn,
    language: book.language || '',
    pages: book.pages || 0,
    description: book.description || '',
    coverUrl: book.coverUrl || '',
    category: book.category,
    stock: book.stock,
    available: book.available,
    status: book.status === 1,
    toc: book.toc || ''
  };
  
  bookFormError.value = '';
  showEditBookModal.value = true;
};

const openAddBookModal = () => {
  bookForm.value = {
    id: 0,
    title: '',
    author: '',
    publisher: '',
    publicationDate: '',
    isbn: '',
    language: '',
    pages: 0,
    description: '',
    coverUrl: '',
    category: '',
    stock: 0,
    available: 0,
    status: true,
    toc: ''
  };
  
  bookFormError.value = '';
  showAddBookModal.value = true;
};

const closeBookModal = () => {
  showAddBookModal.value = false;
  showEditBookModal.value = false;
};

const submitBookForm = async () => {
  if (!bookForm.value.title || !bookForm.value.author || !bookForm.value.isbn || !bookForm.value.category) {
    bookFormError.value = '请填写必填字段';
    return;
  }
  
  isSubmitting.value = true;
  bookFormError.value = '';
  
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (showEditBookModal.value) {
      // 编辑现有图书
      const index = books.value.findIndex(book => book.id === bookForm.value.id);
      if (index !== -1) {
        books.value[index] = {
          ...bookForm.value,
          status: bookForm.value.status ? 1 : 0,
          available: bookForm.value.status ? bookForm.value.stock : 0
        };
        successMessage.value = '图书信息更新成功';
      }
    } else {
      // 添加新图书
      const newBook: Book = {
        ...bookForm.value,
        id: Date.now(), // 模拟生成ID
        status: bookForm.value.status ? 1 : 0,
        available: bookForm.value.status ? bookForm.value.stock : 0
      };
      books.value.unshift(newBook);
      successMessage.value = '新图书添加成功';
    }
    
    // 自动关闭成功消息
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
    
    closeBookModal();
  } catch (e) {
    console.error(e);
    bookFormError.value = '提交失败，请稍后再试';
  } finally {
    isSubmitting.value = false;
  }
};

const confirmDelete = (book: Book) => {
  bookToDelete.value = book;
  deleteError.value = '';
  showDeleteModal.value = true;
};

const deleteBook = async () => {
  if (!bookToDelete.value) return;
  
  isDeleting.value = true;
  deleteError.value = '';
  
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 从列表中删除
    books.value = books.value.filter(book => book.id !== bookToDelete.value?.id);
    
    showDeleteModal.value = false;
    successMessage.value = '图书删除成功';
    
    // 自动关闭成功消息
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (e) {
    console.error(e);
    deleteError.value = '删除失败，请稍后再试';
  } finally {
    isDeleting.value = false;
  }
};

const toggleStatus = async (book: Book) => {
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 更新图书状态
    const index = books.value.findIndex(b => b.id === book.id);
    if (index !== -1) {
      const newStatus = book.status === 1 ? 0 : 1;
      books.value[index] = {
        ...book,
        status: newStatus,
        available: newStatus === 1 ? book.stock : 0
      };
      
      successMessage.value = `图书状态已更新为${newStatus === 1 ? '可借阅' : '不可借阅'}`;
      
      // 自动关闭成功消息
      setTimeout(() => {
        successMessage.value = '';
      }, 3000);
    }
  } catch (e) {
    console.error(e);
    error.value = '更新状态失败，请稍后再试';
    
    // 自动关闭错误消息
    setTimeout(() => {
      error.value = '';
    }, 3000);
  }
};

// 监听过滤器变化
watch([searchQuery, categoryFilter, statusFilter], () => {
  currentPage.value = 1;
});

// 监听分页变化，确保当前页在有效范围内
watch(totalPages, (newTotalPages) => {
  if (currentPage.value > newTotalPages && newTotalPages > 0) {
    currentPage.value = newTotalPages;
  }
});

onMounted(() => {
  fetchBooks();
});
</script>

<style scoped>
.book-management-container {
  padding: 20px;
  position: relative;
}

.book-thumbnail {
  width: 40px;
  height: 55px;
  object-fit: cover;
  border-radius: 2px;
}

.book-title-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
}

.book-title-link:hover {
  color: #0d6efd;
  text-decoration: underline;
}

.fixed-alert {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1050;
  min-width: 250px;
}

.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 表格样式 */
.table {
  font-size: 0.9rem;
}

.table th {
  background-color: #f8f9fa;
  vertical-align: middle;
}

.table td {
  vertical-align: middle;
}
</style> 