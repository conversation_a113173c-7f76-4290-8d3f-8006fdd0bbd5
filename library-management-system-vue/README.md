# 图书管理系统 - Vue前端

这是图书管理系统的现代前端应用，基于Vue 3、TypeScript和Vite构建。

## 项目结构

```
library-management-system-vue/
├── src/                    # 源代码目录
│   ├── assets/             # 静态资源（图片、CSS等）
│   ├── components/         # Vue组件
│   ├── views/              # 页面视图组件
│   ├── router/             # Vue Router配置
│   ├── stores/             # Pinia状态管理
│   ├── utils/              # 工具函数
│   ├── config.ts           # 全局配置
│   ├── App.vue             # 根组件
│   └── main.ts             # 应用入口
├── public/                 # 公共静态资源
├── index.html              # HTML入口
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
└── package.json            # 项目依赖
```

## 功能特性

- 用户管理：管理员可以添加、编辑和删除用户
- 图书管理：管理图书信息，包括添加、编辑和删除图书
- 借阅管理：处理图书借阅、归还和续借
- 统计分析：查看图书借阅统计和趋势图表

## 开发环境设置

1. 安装依赖：
   ```
   npm install
   ```

2. 启动开发服务器：
   ```
   npm run dev
   ```

3. 构建生产版本：
   ```
   npm run build
   ```

## 后端API

后端API服务运行在 `http://localhost:8080/api`。
Vite开发服务器已配置代理，前端访问`/api/*`的请求将自动代理到后端。 