/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated.tables;


import com.library.management.model.generated.Indexes;
import com.library.management.model.generated.Keys;
import com.library.management.model.generated.LibraryManagement;
import com.library.management.model.generated.tables.records.BorrowRecordRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 借阅记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BorrowRecord extends TableImpl<BorrowRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>library_management.borrow_record</code>
     */
    public static final BorrowRecord BORROW_RECORD = new BorrowRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BorrowRecordRecord> getRecordType() {
        return BorrowRecordRecord.class;
    }

    /**
     * The column <code>library_management.borrow_record.id</code>.
     */
    public final TableField<BorrowRecordRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

    /**
     * The column <code>library_management.borrow_record.user_id</code>.
     */
    public final TableField<BorrowRecordRecord, Long> USER_ID = createField(DSL.name("user_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>library_management.borrow_record.book_id</code>.
     */
    public final TableField<BorrowRecordRecord, Long> BOOK_ID = createField(DSL.name("book_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>library_management.borrow_record.borrow_date</code>.
     */
    public final TableField<BorrowRecordRecord, LocalDateTime> BORROW_DATE = createField(DSL.name("borrow_date"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "");

    /**
     * The column <code>library_management.borrow_record.due_date</code>.
     */
    public final TableField<BorrowRecordRecord, LocalDateTime> DUE_DATE = createField(DSL.name("due_date"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "");

    /**
     * The column <code>library_management.borrow_record.return_date</code>.
     */
    public final TableField<BorrowRecordRecord, LocalDateTime> RETURN_DATE = createField(DSL.name("return_date"), SQLDataType.LOCALDATETIME(0), this, "");

    /**
     * The column <code>library_management.borrow_record.status</code>. 1-借出,
     * 2-已还, 3-逾期
     */
    public final TableField<BorrowRecordRecord, Byte> STATUS = createField(DSL.name("status"), SQLDataType.TINYINT.nullable(false).defaultValue(DSL.inline("1", SQLDataType.TINYINT)), this, "1-借出, 2-已还, 3-逾期");

    /**
     * The column <code>library_management.borrow_record.renew_count</code>.
     */
    public final TableField<BorrowRecordRecord, Integer> RENEW_COUNT = createField(DSL.name("renew_count"), SQLDataType.INTEGER.defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>library_management.borrow_record.remarks</code>.
     */
    public final TableField<BorrowRecordRecord, String> REMARKS = createField(DSL.name("remarks"), SQLDataType.VARCHAR(200), this, "");

    private BorrowRecord(Name alias, Table<BorrowRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private BorrowRecord(Name alias, Table<BorrowRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("借阅记录表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>library_management.borrow_record</code> table
     * reference
     */
    public BorrowRecord(String alias) {
        this(DSL.name(alias), BORROW_RECORD);
    }

    /**
     * Create an aliased <code>library_management.borrow_record</code> table
     * reference
     */
    public BorrowRecord(Name alias) {
        this(alias, BORROW_RECORD);
    }

    /**
     * Create a <code>library_management.borrow_record</code> table reference
     */
    public BorrowRecord() {
        this(DSL.name("borrow_record"), null);
    }

    public <O extends Record> BorrowRecord(Table<O> child, ForeignKey<O, BorrowRecordRecord> key) {
        super(child, key, BORROW_RECORD);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : LibraryManagement.LIBRARY_MANAGEMENT;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.BORROW_RECORD_IDX_BOOK_ID, Indexes.BORROW_RECORD_IDX_STATUS, Indexes.BORROW_RECORD_IDX_USER_ID);
    }

    @Override
    public Identity<BorrowRecordRecord, Long> getIdentity() {
        return (Identity<BorrowRecordRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<BorrowRecordRecord> getPrimaryKey() {
        return Keys.KEY_BORROW_RECORD_PRIMARY;
    }

    @Override
    public List<ForeignKey<BorrowRecordRecord, ?>> getReferences() {
        return Arrays.asList(Keys.BORROW_RECORD_IBFK_1, Keys.BORROW_RECORD_IBFK_2);
    }

    private transient User _user;
    private transient Book _book;

    /**
     * Get the implicit join path to the <code>library_management.user</code>
     * table.
     */
    public User user() {
        if (_user == null)
            _user = new User(this, Keys.BORROW_RECORD_IBFK_1);

        return _user;
    }

    /**
     * Get the implicit join path to the <code>library_management.book</code>
     * table.
     */
    public Book book() {
        if (_book == null)
            _book = new Book(this, Keys.BORROW_RECORD_IBFK_2);

        return _book;
    }

    @Override
    public BorrowRecord as(String alias) {
        return new BorrowRecord(DSL.name(alias), this);
    }

    @Override
    public BorrowRecord as(Name alias) {
        return new BorrowRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public BorrowRecord rename(String name) {
        return new BorrowRecord(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public BorrowRecord rename(Name name) {
        return new BorrowRecord(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, Long, Long, LocalDateTime, LocalDateTime, LocalDateTime, Byte, Integer, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
