package com.library.management.repository;

import com.library.management.model.entity.User;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import static org.jooq.impl.DSL.*;

import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public class UserRepository {
    
    private final DSLContext dsl;
    
    @Autowired
    public UserRepository(DSLContext dsl) {
        this.dsl = dsl;
    }
    
    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户信息（可能为空）
     */
    public Optional<User> findByUsername(String username) {
        // 使用jOOQ动态SQL构建查询
        var result = dsl.select()
                .from("user")
                .where("username = ?", username)
                .fetchOneInto(User.class);
        
        return Optional.ofNullable(result);
    }
    
    /**
     * 添加新用户
     *
     * @param user 用户信息
     * @return 添加成功后的用户（含ID）
     */
    public User save(User user) {
        if (user.getId() == null) {
            // 新增用户
            var record = dsl.insertInto(
                        table("user"),
                        field("username"),
                        field("password"),
                        field("real_name"),
                        field("email"),
                        field("phone"),
                        field("role"),
                        field("status")
                )
                .values(
                        user.getUsername(),
                        user.getPassword(),
                        user.getRealName(),
                        user.getEmail(),
                        user.getPhone(),
                        user.getRole(),
                        user.getStatus()
                )
                .returning()
                .fetchOne();
            
            if (record != null) {
                user.setId(record.getValue(field("id", Long.class)));
            }
        } else {
            // 更新用户
            dsl.update(table("user"))
                .set(field("username"), user.getUsername())
                .set(field("real_name"), user.getRealName())
                .set(field("email"), user.getEmail())
                .set(field("phone"), user.getPhone())
                .set(field("role"), user.getRole())
                .set(field("status"), user.getStatus())
                .where(field("id").eq(user.getId()))
                .execute();
            
            // 注意：不直接更新密码，密码应该通过专门接口修改
        }
        
        return user;
    }
    
    /**
     * 更新用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码（应已加密）
     * @return 是否更新成功
     */
    public boolean updatePassword(Long userId, String newPassword) {
        return dsl.update(table("user"))
                .set(field("password"), newPassword)
                .where(field("id").eq(userId))
                .execute() > 0;
    }
    
    /**
     * 获取所有用户列表
     *
     * @return 用户列表
     */
    public List<User> findAll() {
        return dsl.select()
                .from("user")
                .orderBy(field("id"))
                .fetchInto(User.class);
    }
    
    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 用户信息（可能为空）
     */
    public Optional<User> findById(Long id) {
        var result = dsl.select()
                .from("user")
                .where("id = ?", id)
                .fetchOneInto(User.class);
        
        return Optional.ofNullable(result);
    }
    
    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否删除成功
     */
    public boolean deleteById(Long id) {
        return dsl.deleteFrom(table("user"))
                .where(field("id").eq(id))
                .execute() > 0;
    }
    
    /**
     * 修改用户状态
     *
     * @param id 用户ID
     * @param status 新状态（0-禁用，1-正常）
     * @return 是否更新成功
     */
    public boolean updateStatus(Long id, Integer status) {
        return dsl.update(table("user"))
                .set(field("status"), status)
                .where(field("id").eq(id))
                .execute() > 0;
    }
} 