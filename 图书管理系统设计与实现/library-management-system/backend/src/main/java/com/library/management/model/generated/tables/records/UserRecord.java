/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated.tables.records;


import com.library.management.model.generated.tables.User;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserRecord extends UpdatableRecordImpl<UserRecord> implements Record10<Long, String, String, String, String, String, Byte, Byte, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>library_management.user.id</code>.
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>library_management.user.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>library_management.user.username</code>.
     */
    public void setUsername(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>library_management.user.username</code>.
     */
    public String getUsername() {
        return (String) get(1);
    }

    /**
     * Setter for <code>library_management.user.password</code>.
     */
    public void setPassword(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>library_management.user.password</code>.
     */
    public String getPassword() {
        return (String) get(2);
    }

    /**
     * Setter for <code>library_management.user.real_name</code>.
     */
    public void setRealName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>library_management.user.real_name</code>.
     */
    public String getRealName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>library_management.user.email</code>.
     */
    public void setEmail(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>library_management.user.email</code>.
     */
    public String getEmail() {
        return (String) get(4);
    }

    /**
     * Setter for <code>library_management.user.phone</code>.
     */
    public void setPhone(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>library_management.user.phone</code>.
     */
    public String getPhone() {
        return (String) get(5);
    }

    /**
     * Setter for <code>library_management.user.role</code>. 1-普通用户，2-管理员
     */
    public void setRole(Byte value) {
        set(6, value);
    }

    /**
     * Getter for <code>library_management.user.role</code>. 1-普通用户，2-管理员
     */
    public Byte getRole() {
        return (Byte) get(6);
    }

    /**
     * Setter for <code>library_management.user.status</code>. 0-禁用, 1-正常
     */
    public void setStatus(Byte value) {
        set(7, value);
    }

    /**
     * Getter for <code>library_management.user.status</code>. 0-禁用, 1-正常
     */
    public Byte getStatus() {
        return (Byte) get(7);
    }

    /**
     * Setter for <code>library_management.user.created_time</code>.
     */
    public void setCreatedTime(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>library_management.user.created_time</code>.
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>library_management.user.updated_time</code>.
     */
    public void setUpdatedTime(LocalDateTime value) {
        set(9, value);
    }

    /**
     * Getter for <code>library_management.user.updated_time</code>.
     */
    public LocalDateTime getUpdatedTime() {
        return (LocalDateTime) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row10<Long, String, String, String, String, String, Byte, Byte, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    @Override
    public Row10<Long, String, String, String, String, String, Byte, Byte, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row10) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return User.USER.ID;
    }

    @Override
    public Field<String> field2() {
        return User.USER.USERNAME;
    }

    @Override
    public Field<String> field3() {
        return User.USER.PASSWORD;
    }

    @Override
    public Field<String> field4() {
        return User.USER.REAL_NAME;
    }

    @Override
    public Field<String> field5() {
        return User.USER.EMAIL;
    }

    @Override
    public Field<String> field6() {
        return User.USER.PHONE;
    }

    @Override
    public Field<Byte> field7() {
        return User.USER.ROLE;
    }

    @Override
    public Field<Byte> field8() {
        return User.USER.STATUS;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return User.USER.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return User.USER.UPDATED_TIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getUsername();
    }

    @Override
    public String component3() {
        return getPassword();
    }

    @Override
    public String component4() {
        return getRealName();
    }

    @Override
    public String component5() {
        return getEmail();
    }

    @Override
    public String component6() {
        return getPhone();
    }

    @Override
    public Byte component7() {
        return getRole();
    }

    @Override
    public Byte component8() {
        return getStatus();
    }

    @Override
    public LocalDateTime component9() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component10() {
        return getUpdatedTime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getUsername();
    }

    @Override
    public String value3() {
        return getPassword();
    }

    @Override
    public String value4() {
        return getRealName();
    }

    @Override
    public String value5() {
        return getEmail();
    }

    @Override
    public String value6() {
        return getPhone();
    }

    @Override
    public Byte value7() {
        return getRole();
    }

    @Override
    public Byte value8() {
        return getStatus();
    }

    @Override
    public LocalDateTime value9() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value10() {
        return getUpdatedTime();
    }

    @Override
    public UserRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public UserRecord value2(String value) {
        setUsername(value);
        return this;
    }

    @Override
    public UserRecord value3(String value) {
        setPassword(value);
        return this;
    }

    @Override
    public UserRecord value4(String value) {
        setRealName(value);
        return this;
    }

    @Override
    public UserRecord value5(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public UserRecord value6(String value) {
        setPhone(value);
        return this;
    }

    @Override
    public UserRecord value7(Byte value) {
        setRole(value);
        return this;
    }

    @Override
    public UserRecord value8(Byte value) {
        setStatus(value);
        return this;
    }

    @Override
    public UserRecord value9(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public UserRecord value10(LocalDateTime value) {
        setUpdatedTime(value);
        return this;
    }

    @Override
    public UserRecord values(Long value1, String value2, String value3, String value4, String value5, String value6, Byte value7, Byte value8, LocalDateTime value9, LocalDateTime value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserRecord
     */
    public UserRecord() {
        super(User.USER);
    }

    /**
     * Create a detached, initialised UserRecord
     */
    public UserRecord(Long id, String username, String password, String realName, String email, String phone, Byte role, Byte status, LocalDateTime createdTime, LocalDateTime updatedTime) {
        super(User.USER);

        setId(id);
        setUsername(username);
        setPassword(password);
        setRealName(realName);
        setEmail(email);
        setPhone(phone);
        setRole(role);
        setStatus(status);
        setCreatedTime(createdTime);
        setUpdatedTime(updatedTime);
    }
}
