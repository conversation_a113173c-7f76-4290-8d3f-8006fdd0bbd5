package com.library.management.repository;

import com.library.management.model.entity.Category;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static org.jooq.impl.DSL.*;

/**
 * 图书分类数据访问层
 */
@Repository
public class CategoryRepository {

    private final DSLContext dsl;

    @Autowired
    public CategoryRepository(DSLContext dsl) {
        this.dsl = dsl;
    }

    /**
     * 查找所有分类
     *
     * @return 分类列表
     */
    public List<Category> findAll() {
        return dsl.select()
                .from("category")
                .orderBy(field("id"))
                .fetchInto(Category.class);
    }

    /**
     * 根据ID查找分类
     *
     * @param id 分类ID
     * @return 分类信息（可能为空）
     */
    public Optional<Category> findById(Long id) {
        var result = dsl.select()
                .from("category")
                .where(field("id").eq(id))
                .fetchOneInto(Category.class);

        return Optional.ofNullable(result);
    }

    /**
     * 保存分类（新增或更新）
     *
     * @param category 分类信息
     * @return 保存后的分类（包含ID）
     */
    public Category save(Category category) {
        if (category.getId() == null) {
            // 新增分类
            var record = dsl.insertInto(
                            table("category"),
                            field("category_name"),
                            field("parent_id"),
                            field("description")
                    )
                    .values(
                            category.getCategoryName(),
                            category.getParentId(),
                            category.getDescription()
                    )
                    .returning()
                    .fetchOne();

            if (record != null) {
                category.setId(record.getValue(field("id", Long.class)));
            }
        } else {
            // 更新分类
            dsl.update(table("category"))
                    .set(field("category_name"), category.getCategoryName())
                    .set(field("parent_id"), category.getParentId())
                    .set(field("description"), category.getDescription())
                    .where(field("id").eq(category.getId()))
                    .execute();
        }

        return category;
    }

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 是否删除成功
     */
    public boolean deleteById(Long id) {
        return dsl.deleteFrom(table("category"))
                .where(field("id").eq(id))
                .execute() > 0;
    }

    /**
     * 查找所有顶层分类（没有父分类的）
     *
     * @return 分类列表
     */
    public List<Category> findTopCategories() {
        return dsl.select()
                .from("category")
                .where(field("parent_id").isNull())
                .orderBy(field("category_name"))
                .fetchInto(Category.class);
    }

    /**
     * 查找子分类
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    public List<Category> findByParentId(Long parentId) {
        return dsl.select()
                .from("category")
                .where(field("parent_id").eq(parentId))
                .orderBy(field("category_name"))
                .fetchInto(Category.class);
    }

    /**
     * 根据分类名称查找分类
     *
     * @param categoryName 分类名称
     * @return 分类信息（可能为空）
     */
    public Optional<Category> findByCategoryName(String categoryName) {
        var result = dsl.select()
                .from("category")
                .where(field("category_name").eq(categoryName))
                .fetchOneInto(Category.class);

        return Optional.ofNullable(result);
    }
} 