package com.library.management.service;

import com.library.management.model.entity.Book;
import com.library.management.model.entity.BorrowRecord;
import com.library.management.model.entity.User;
import com.library.management.repository.BookRepository;
import com.library.management.repository.BorrowRepository;
import com.library.management.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

/**
 * 借阅服务
 */
@Service
public class BorrowService {

    private final BorrowRepository borrowRepository;
    private final BookRepository bookRepository;
    private final UserRepository userRepository;
    
    private static final int DEFAULT_BORROW_DAYS = 30; // 默认借阅期限（天）
    private static final int DEFAULT_RENEW_DAYS = 15;  // 默认续借期限（天）
    private static final int MAX_RENEW_COUNT = 2;      // 最大续借次数

    @Autowired
    public BorrowService(BorrowRepository borrowRepository, BookRepository bookRepository, UserRepository userRepository) {
        this.borrowRepository = borrowRepository;
        this.bookRepository = bookRepository;
        this.userRepository = userRepository;
    }

    /**
     * 获取所有借阅记录
     *
     * @return 借阅记录列表
     */
    public List<BorrowRecord> getAllBorrows() {
        return borrowRepository.findAll();
    }

    /**
     * 根据ID查找借阅记录
     *
     * @param id 借阅记录ID
     * @return 借阅记录（可能为空）
     */
    public Optional<BorrowRecord> getBorrowById(Long id) {
        return borrowRepository.findById(id);
    }

    /**
     * 查找用户的借阅记录
     *
     * @param userId 用户ID
     * @return 借阅记录列表
     */
    public List<BorrowRecord> getBorrowsByUserId(Long userId) {
        // 检查用户是否存在
        if (userRepository.findById(userId).isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        return borrowRepository.findByUserId(userId);
    }

    /**
     * 查找图书的借阅记录
     *
     * @param bookId 图书ID
     * @return 借阅记录列表
     */
    public List<BorrowRecord> getBorrowsByBookId(Long bookId) {
        // 检查图书是否存在
        if (bookRepository.findById(bookId).isEmpty()) {
            throw new IllegalArgumentException("图书不存在");
        }
        
        return borrowRepository.findByBookId(bookId);
    }
    
    /**
     * 借阅图书
     *
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 借阅记录
     * @throws IllegalArgumentException 如果用户或图书不存在，或图书无可借副本
     */
    @Transactional
    public BorrowRecord borrowBook(Long userId, Long bookId) {
        // 检查用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 检查图书是否存在
        Optional<Book> bookOpt = bookRepository.findById(bookId);
        if (bookOpt.isEmpty()) {
            throw new IllegalArgumentException("图书不存在");
        }
        
        Book book = bookOpt.get();
        
        // 检查图书是否有可借副本
        if (book.getAvailableCopies() <= 0) {
            throw new IllegalArgumentException("图书无可借副本");
        }
        
        // 创建借阅记录
        BorrowRecord borrowRecord = new BorrowRecord();
        borrowRecord.setUserId(userId);
        borrowRecord.setBookId(bookId);
        borrowRecord.setBorrowDate(LocalDateTime.now());
        borrowRecord.setDueDate(LocalDateTime.now().plusDays(DEFAULT_BORROW_DAYS));
        borrowRecord.setStatus(1); // 1-借出状态
        borrowRecord.setRenewCount(0);
        
        // 保存借阅记录
        BorrowRecord savedRecord = borrowRepository.save(borrowRecord);
        
        // 更新图书可借数量
        book.setAvailableCopies(book.getAvailableCopies() - 1);
        bookRepository.save(book);
        
        return savedRecord;
    }
    
    /**
     * 归还图书
     *
     * @param id 借阅记录ID
     * @return 更新后的借阅记录
     * @throws IllegalArgumentException 如果借阅记录不存在或已归还
     */
    @Transactional
    public BorrowRecord returnBook(Long id) {
        // 查找借阅记录
        Optional<BorrowRecord> borrowOpt = borrowRepository.findById(id);
        if (borrowOpt.isEmpty()) {
            throw new IllegalArgumentException("借阅记录不存在");
        }
        
        BorrowRecord borrow = borrowOpt.get();
        
        // 检查是否已归还
        if (borrow.getStatus() == 2) { // 2-已还状态
            throw new IllegalArgumentException("图书已归还");
        }
        
        // 更新借阅记录
        LocalDateTime now = LocalDateTime.now();
        borrow.setReturnDate(now);
        
        // 检查是否逾期
        if (now.isAfter(borrow.getDueDate())) {
            borrow.setStatus(3); // 3-逾期状态
        } else {
            borrow.setStatus(2); // 2-已还状态
        }
        
        // 保存更新的借阅记录
        BorrowRecord updatedBorrow = borrowRepository.save(borrow);
        
        // 更新图书可借数量
        Optional<Book> bookOpt = bookRepository.findById(borrow.getBookId());
        if (bookOpt.isPresent()) {
            Book book = bookOpt.get();
            book.setAvailableCopies(book.getAvailableCopies() + 1);
            bookRepository.save(book);
        }
        
        return updatedBorrow;
    }
    
    /**
     * 续借图书
     *
     * @param id 借阅记录ID
     * @return 更新后的借阅记录
     * @throws IllegalArgumentException 如果借阅记录不存在、已归还或超过最大续借次数
     */
    @Transactional
    public BorrowRecord renewBook(Long id) {
        // 查找借阅记录
        Optional<BorrowRecord> borrowOpt = borrowRepository.findById(id);
        if (borrowOpt.isEmpty()) {
            throw new IllegalArgumentException("借阅记录不存在");
        }
        
        BorrowRecord borrow = borrowOpt.get();
        
        // 检查是否已归还
        if (borrow.getStatus() == 2) { // 2-已还状态
            throw new IllegalArgumentException("图书已归还，无法续借");
        }
        
        // 检查续借次数
        if (borrow.getRenewCount() >= MAX_RENEW_COUNT) {
            throw new IllegalArgumentException("已达到最大续借次数");
        }
        
        // 更新借阅记录
        borrow.setDueDate(LocalDateTime.now().plusDays(DEFAULT_RENEW_DAYS));
        borrow.setRenewCount(borrow.getRenewCount() + 1);
        
        // 保存更新的借阅记录
        return borrowRepository.save(borrow);
    }
    
    /**
     * 查找用户当前的借阅（未归还）
     *
     * @param userId 用户ID
     * @return 借阅记录列表
     */
    public List<BorrowRecord> getCurrentBorrows(Long userId) {
        return borrowRepository.findCurrentBorrows(userId);
    }
    
    /**
     * 查找所有逾期的借阅记录
     *
     * @return 逾期的借阅记录列表
     */
    public List<BorrowRecord> getOverdueRecords() {
        return borrowRepository.findOverdueRecords();
    }
    
    /**
     * 计算借阅的逾期天数
     *
     * @param borrow 借阅记录
     * @return 逾期天数（如果未逾期，返回0）
     */
    public long calculateOverdueDays(BorrowRecord borrow) {
        if (borrow.getStatus() == 2) { // 已归还
            // 如果归还日期晚于应还日期，计算逾期天数
            if (borrow.getReturnDate() != null && borrow.getReturnDate().isAfter(borrow.getDueDate())) {
                return ChronoUnit.DAYS.between(borrow.getDueDate(), borrow.getReturnDate());
            }
        } else if (borrow.getStatus() == 1) { // 借出状态
            // 如果当前日期晚于应还日期，计算逾期天数
            LocalDateTime now = LocalDateTime.now();
            if (now.isAfter(borrow.getDueDate())) {
                return ChronoUnit.DAYS.between(borrow.getDueDate(), now);
            }
        }
        
        return 0; // 未逾期
    }
    
    /**
     * 计算借阅的剩余天数
     *
     * @param borrow 借阅记录
     * @return 剩余天数（如果已归还或已逾期，返回0）
     */
    public long calculateRemainingDays(BorrowRecord borrow) {
        if (borrow.getStatus() == 1) { // 借出状态
            LocalDateTime now = LocalDateTime.now();
            if (borrow.getDueDate().isAfter(now)) {
                return ChronoUnit.DAYS.between(now, borrow.getDueDate());
            }
        }
        
        return 0; // 已归还或已逾期
    }
} 