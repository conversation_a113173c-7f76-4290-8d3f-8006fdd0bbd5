package com.library.management.service;

import com.library.management.model.entity.Book;
import com.library.management.repository.BookRepository;
import com.library.management.repository.CategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 图书服务
 */
@Service
public class BookService {

    private final BookRepository bookRepository;
    private final CategoryRepository categoryRepository;

    @Autowired
    public BookService(BookRepository bookRepository, CategoryRepository categoryRepository) {
        this.bookRepository = bookRepository;
        this.categoryRepository = categoryRepository;
    }

    /**
     * 获取所有图书
     *
     * @return 图书列表
     */
    public List<Book> getAllBooks() {
        return bookRepository.findAll();
    }

    /**
     * 根据ID查找图书
     *
     * @param id 图书ID
     * @return 图书信息（可能为空）
     */
    public Optional<Book> getBookById(Long id) {
        return bookRepository.findById(id);
    }

    /**
     * 根据ISBN查找图书
     *
     * @param isbn ISBN
     * @return 图书信息（可能为空）
     */
    public Optional<Book> getBookByIsbn(String isbn) {
        return bookRepository.findByIsbn(isbn);
    }

    /**
     * 搜索图书
     *
     * @param title      书名（可选）
     * @param author     作者（可选）
     * @param categoryId 分类ID（可选）
     * @return 符合条件的图书列表
     */
    public List<Book> searchBooks(String title, String author, Long categoryId) {
        return bookRepository.search(title, author, categoryId);
    }

    /**
     * 添加新图书
     *
     * @param book 图书信息
     * @return 添加后的图书（包含ID）
     * @throws IllegalArgumentException 如果ISBN已存在
     */
    public Book addBook(Book book) {
        // 检查ISBN是否已存在
        Optional<Book> existingBook = bookRepository.findByIsbn(book.getIsbn());
        if (existingBook.isPresent()) {
            throw new IllegalArgumentException("ISBN已存在");
        }

        // 检查分类是否存在
        if (book.getCategoryId() != null) {
            if (categoryRepository.findById(book.getCategoryId()).isEmpty()) {
                throw new IllegalArgumentException("图书分类不存在");
            }
        }

        // 设置可借数量等于总数量（如果未设置）
        if (book.getAvailableCopies() == null) {
            book.setAvailableCopies(book.getTotalCopies());
        }

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        book.setCreatedTime(now);
        book.setUpdatedTime(now);

        return bookRepository.save(book);
    }

    /**
     * 更新图书信息
     *
     * @param id   图书ID
     * @param book 更新的图书信息
     * @return 更新后的图书信息
     * @throws IllegalArgumentException 如果图书不存在或ISBN已被其他图书使用
     */
    public Book updateBook(Long id, Book book) {
        // 确保图书存在
        Optional<Book> existingBookOpt = bookRepository.findById(id);
        if (existingBookOpt.isEmpty()) {
            throw new IllegalArgumentException("图书不存在");
        }

        Book existingBook = existingBookOpt.get();

        // 如果更新ISBN，检查是否与其他图书冲突
        if (!existingBook.getIsbn().equals(book.getIsbn())) {
            Optional<Book> conflictBook = bookRepository.findByIsbn(book.getIsbn());
            if (conflictBook.isPresent() && !conflictBook.get().getId().equals(id)) {
                throw new IllegalArgumentException("ISBN已被其他图书使用");
            }
        }

        // 检查分类是否存在
        if (book.getCategoryId() != null) {
            if (categoryRepository.findById(book.getCategoryId()).isEmpty()) {
                throw new IllegalArgumentException("图书分类不存在");
            }
        }

        // 计算可借数量的调整
        int availableDiff = 0;
        if (book.getTotalCopies() != null && existingBook.getTotalCopies() != null) {
            availableDiff = book.getTotalCopies() - existingBook.getTotalCopies();
        }
        
        if (book.getAvailableCopies() == null) {
            // 如果未设置可借数量，则根据总数量变化调整
            if (existingBook.getAvailableCopies() != null) {
                book.setAvailableCopies(existingBook.getAvailableCopies() + availableDiff);
            }
        }

        // 设置ID和时间
        book.setId(id);
        book.setCreatedTime(existingBook.getCreatedTime());
        book.setUpdatedTime(LocalDateTime.now());

        return bookRepository.save(book);
    }

    /**
     * 删除图书
     *
     * @param id 图书ID
     * @return 是否删除成功
     */
    public boolean deleteBook(Long id) {
        // 检查图书是否存在
        if (bookRepository.findById(id).isEmpty()) {
            return false;
        }

        return bookRepository.deleteById(id);
    }

    /**
     * 更新图书可借数量
     *
     * @param id        图书ID
     * @param available 新的可借数量
     * @return 是否更新成功
     */
    public boolean updateAvailableCopies(Long id, Integer available) {
        // 检查图书是否存在
        Optional<Book> bookOpt = bookRepository.findById(id);
        if (bookOpt.isEmpty()) {
            return false;
        }

        Book book = bookOpt.get();
        
        // 确保可借数量不超过总数量
        if (available > book.getTotalCopies()) {
            return false;
        }

        return bookRepository.updateAvailableCopies(id, available);
    }

    /**
     * 借出图书（减少可借数量）
     *
     * @param id 图书ID
     * @return 是否借出成功
     */
    public boolean borrowBook(Long id) {
        // 检查图书是否存在
        Optional<Book> bookOpt = bookRepository.findById(id);
        if (bookOpt.isEmpty()) {
            return false;
        }

        Book book = bookOpt.get();
        
        // 检查是否还有可借副本
        if (book.getAvailableCopies() <= 0) {
            return false;
        }

        // 减少可借数量
        return bookRepository.updateAvailableCopies(id, book.getAvailableCopies() - 1);
    }

    /**
     * 归还图书（增加可借数量）
     *
     * @param id 图书ID
     * @return 是否归还成功
     */
    public boolean returnBook(Long id) {
        // 检查图书是否存在
        Optional<Book> bookOpt = bookRepository.findById(id);
        if (bookOpt.isEmpty()) {
            return false;
        }

        Book book = bookOpt.get();
        
        // 检查可借数量是否已达到总数量
        if (book.getAvailableCopies() >= book.getTotalCopies()) {
            return false;
        }

        // 增加可借数量
        return bookRepository.updateAvailableCopies(id, book.getAvailableCopies() + 1);
    }
} 