/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated.tables.records;


import com.library.management.model.generated.tables.Category;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 图书分类表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CategoryRecord extends UpdatableRecordImpl<CategoryRecord> implements Record6<Long, String, Long, String, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>library_management.category.id</code>.
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>library_management.category.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>library_management.category.category_name</code>.
     */
    public void setCategoryName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>library_management.category.category_name</code>.
     */
    public String getCategoryName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>library_management.category.parent_id</code>.
     */
    public void setParentId(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>library_management.category.parent_id</code>.
     */
    public Long getParentId() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>library_management.category.description</code>.
     */
    public void setDescription(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>library_management.category.description</code>.
     */
    public String getDescription() {
        return (String) get(3);
    }

    /**
     * Setter for <code>library_management.category.created_time</code>.
     */
    public void setCreatedTime(LocalDateTime value) {
        set(4, value);
    }

    /**
     * Getter for <code>library_management.category.created_time</code>.
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>library_management.category.updated_time</code>.
     */
    public void setUpdatedTime(LocalDateTime value) {
        set(5, value);
    }

    /**
     * Getter for <code>library_management.category.updated_time</code>.
     */
    public LocalDateTime getUpdatedTime() {
        return (LocalDateTime) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, String, Long, String, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Long, String, Long, String, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return Category.CATEGORY.ID;
    }

    @Override
    public Field<String> field2() {
        return Category.CATEGORY.CATEGORY_NAME;
    }

    @Override
    public Field<Long> field3() {
        return Category.CATEGORY.PARENT_ID;
    }

    @Override
    public Field<String> field4() {
        return Category.CATEGORY.DESCRIPTION;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return Category.CATEGORY.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return Category.CATEGORY.UPDATED_TIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getCategoryName();
    }

    @Override
    public Long component3() {
        return getParentId();
    }

    @Override
    public String component4() {
        return getDescription();
    }

    @Override
    public LocalDateTime component5() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component6() {
        return getUpdatedTime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getCategoryName();
    }

    @Override
    public Long value3() {
        return getParentId();
    }

    @Override
    public String value4() {
        return getDescription();
    }

    @Override
    public LocalDateTime value5() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value6() {
        return getUpdatedTime();
    }

    @Override
    public CategoryRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public CategoryRecord value2(String value) {
        setCategoryName(value);
        return this;
    }

    @Override
    public CategoryRecord value3(Long value) {
        setParentId(value);
        return this;
    }

    @Override
    public CategoryRecord value4(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public CategoryRecord value5(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public CategoryRecord value6(LocalDateTime value) {
        setUpdatedTime(value);
        return this;
    }

    @Override
    public CategoryRecord values(Long value1, String value2, Long value3, String value4, LocalDateTime value5, LocalDateTime value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CategoryRecord
     */
    public CategoryRecord() {
        super(Category.CATEGORY);
    }

    /**
     * Create a detached, initialised CategoryRecord
     */
    public CategoryRecord(Long id, String categoryName, Long parentId, String description, LocalDateTime createdTime, LocalDateTime updatedTime) {
        super(Category.CATEGORY);

        setId(id);
        setCategoryName(categoryName);
        setParentId(parentId);
        setDescription(description);
        setCreatedTime(createdTime);
        setUpdatedTime(updatedTime);
    }
}
