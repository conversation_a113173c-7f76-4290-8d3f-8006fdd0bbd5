package com.library.management.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 借阅记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BorrowRecord {
    /**
     * 借阅ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 图书ID
     */
    private Long bookId;
    
    /**
     * 借阅日期
     */
    private LocalDateTime borrowDate;
    
    /**
     * 应还日期
     */
    private LocalDateTime dueDate;
    
    /**
     * 实际归还日期
     */
    private LocalDateTime returnDate;
    
    /**
     * 状态（1-借出, 2-已还, 3-逾期）
     */
    private Integer status;
    
    /**
     * 续借次数
     */
    private Integer renewCount;
    
    /**
     * 备注
     */
    private String remarks;
} 