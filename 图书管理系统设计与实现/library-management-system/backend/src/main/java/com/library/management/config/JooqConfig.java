package com.library.management.config;

import org.jooq.conf.RenderNameCase;
import org.jooq.conf.RenderQuotedNames;
import org.jooq.conf.Settings;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * jOOQ配置类 - 只负责jOOQ设置，不再负责DSLContext
 */
@Configuration
public class JooqConfig {

    /**
     * 定制jOOQ Settings
     *
     * @return jOOQ设置
     */
    @Bean
    public Settings jooqSettings() {
        return new Settings()
                // 打印格式化的SQL
                .withRenderFormatted(true)
                // 引用标识符策略
                .withRenderQuotedNames(RenderQuotedNames.EXPLICIT_DEFAULT_UNQUOTED)
                // 标识符大小写渲染
                .withRenderNameCase(RenderNameCase.LOWER)
                // 抓取大小，类似于JDBC的fetchSize
                .withFetchSize(100);
    }
} 