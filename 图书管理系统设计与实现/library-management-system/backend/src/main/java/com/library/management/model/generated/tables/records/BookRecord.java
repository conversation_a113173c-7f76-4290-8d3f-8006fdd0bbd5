/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated.tables.records;


import com.library.management.model.generated.tables.Book;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 图书表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BookRecord extends UpdatableRecordImpl<BookRecord> implements Record14<Long, String, String, String, String, LocalDate, BigDecimal, String, String, Long, Integer, Integer, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>library_management.book.id</code>.
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>library_management.book.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>library_management.book.isbn</code>.
     */
    public void setIsbn(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>library_management.book.isbn</code>.
     */
    public String getIsbn() {
        return (String) get(1);
    }

    /**
     * Setter for <code>library_management.book.title</code>.
     */
    public void setTitle(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>library_management.book.title</code>.
     */
    public String getTitle() {
        return (String) get(2);
    }

    /**
     * Setter for <code>library_management.book.author</code>.
     */
    public void setAuthor(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>library_management.book.author</code>.
     */
    public String getAuthor() {
        return (String) get(3);
    }

    /**
     * Setter for <code>library_management.book.publisher</code>.
     */
    public void setPublisher(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>library_management.book.publisher</code>.
     */
    public String getPublisher() {
        return (String) get(4);
    }

    /**
     * Setter for <code>library_management.book.publish_date</code>.
     */
    public void setPublishDate(LocalDate value) {
        set(5, value);
    }

    /**
     * Getter for <code>library_management.book.publish_date</code>.
     */
    public LocalDate getPublishDate() {
        return (LocalDate) get(5);
    }

    /**
     * Setter for <code>library_management.book.price</code>.
     */
    public void setPrice(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>library_management.book.price</code>.
     */
    public BigDecimal getPrice() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>library_management.book.description</code>.
     */
    public void setDescription(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>library_management.book.description</code>.
     */
    public String getDescription() {
        return (String) get(7);
    }

    /**
     * Setter for <code>library_management.book.cover_url</code>.
     */
    public void setCoverUrl(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>library_management.book.cover_url</code>.
     */
    public String getCoverUrl() {
        return (String) get(8);
    }

    /**
     * Setter for <code>library_management.book.category_id</code>.
     */
    public void setCategoryId(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>library_management.book.category_id</code>.
     */
    public Long getCategoryId() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>library_management.book.total_copies</code>.
     */
    public void setTotalCopies(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>library_management.book.total_copies</code>.
     */
    public Integer getTotalCopies() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>library_management.book.available_copies</code>.
     */
    public void setAvailableCopies(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>library_management.book.available_copies</code>.
     */
    public Integer getAvailableCopies() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>library_management.book.created_time</code>.
     */
    public void setCreatedTime(LocalDateTime value) {
        set(12, value);
    }

    /**
     * Getter for <code>library_management.book.created_time</code>.
     */
    public LocalDateTime getCreatedTime() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>library_management.book.updated_time</code>.
     */
    public void setUpdatedTime(LocalDateTime value) {
        set(13, value);
    }

    /**
     * Getter for <code>library_management.book.updated_time</code>.
     */
    public LocalDateTime getUpdatedTime() {
        return (LocalDateTime) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, String, String, String, String, LocalDate, BigDecimal, String, String, Long, Integer, Integer, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<Long, String, String, String, String, LocalDate, BigDecimal, String, String, Long, Integer, Integer, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return Book.BOOK.ID;
    }

    @Override
    public Field<String> field2() {
        return Book.BOOK.ISBN;
    }

    @Override
    public Field<String> field3() {
        return Book.BOOK.TITLE;
    }

    @Override
    public Field<String> field4() {
        return Book.BOOK.AUTHOR;
    }

    @Override
    public Field<String> field5() {
        return Book.BOOK.PUBLISHER;
    }

    @Override
    public Field<LocalDate> field6() {
        return Book.BOOK.PUBLISH_DATE;
    }

    @Override
    public Field<BigDecimal> field7() {
        return Book.BOOK.PRICE;
    }

    @Override
    public Field<String> field8() {
        return Book.BOOK.DESCRIPTION;
    }

    @Override
    public Field<String> field9() {
        return Book.BOOK.COVER_URL;
    }

    @Override
    public Field<Long> field10() {
        return Book.BOOK.CATEGORY_ID;
    }

    @Override
    public Field<Integer> field11() {
        return Book.BOOK.TOTAL_COPIES;
    }

    @Override
    public Field<Integer> field12() {
        return Book.BOOK.AVAILABLE_COPIES;
    }

    @Override
    public Field<LocalDateTime> field13() {
        return Book.BOOK.CREATED_TIME;
    }

    @Override
    public Field<LocalDateTime> field14() {
        return Book.BOOK.UPDATED_TIME;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getIsbn();
    }

    @Override
    public String component3() {
        return getTitle();
    }

    @Override
    public String component4() {
        return getAuthor();
    }

    @Override
    public String component5() {
        return getPublisher();
    }

    @Override
    public LocalDate component6() {
        return getPublishDate();
    }

    @Override
    public BigDecimal component7() {
        return getPrice();
    }

    @Override
    public String component8() {
        return getDescription();
    }

    @Override
    public String component9() {
        return getCoverUrl();
    }

    @Override
    public Long component10() {
        return getCategoryId();
    }

    @Override
    public Integer component11() {
        return getTotalCopies();
    }

    @Override
    public Integer component12() {
        return getAvailableCopies();
    }

    @Override
    public LocalDateTime component13() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime component14() {
        return getUpdatedTime();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getIsbn();
    }

    @Override
    public String value3() {
        return getTitle();
    }

    @Override
    public String value4() {
        return getAuthor();
    }

    @Override
    public String value5() {
        return getPublisher();
    }

    @Override
    public LocalDate value6() {
        return getPublishDate();
    }

    @Override
    public BigDecimal value7() {
        return getPrice();
    }

    @Override
    public String value8() {
        return getDescription();
    }

    @Override
    public String value9() {
        return getCoverUrl();
    }

    @Override
    public Long value10() {
        return getCategoryId();
    }

    @Override
    public Integer value11() {
        return getTotalCopies();
    }

    @Override
    public Integer value12() {
        return getAvailableCopies();
    }

    @Override
    public LocalDateTime value13() {
        return getCreatedTime();
    }

    @Override
    public LocalDateTime value14() {
        return getUpdatedTime();
    }

    @Override
    public BookRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public BookRecord value2(String value) {
        setIsbn(value);
        return this;
    }

    @Override
    public BookRecord value3(String value) {
        setTitle(value);
        return this;
    }

    @Override
    public BookRecord value4(String value) {
        setAuthor(value);
        return this;
    }

    @Override
    public BookRecord value5(String value) {
        setPublisher(value);
        return this;
    }

    @Override
    public BookRecord value6(LocalDate value) {
        setPublishDate(value);
        return this;
    }

    @Override
    public BookRecord value7(BigDecimal value) {
        setPrice(value);
        return this;
    }

    @Override
    public BookRecord value8(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public BookRecord value9(String value) {
        setCoverUrl(value);
        return this;
    }

    @Override
    public BookRecord value10(Long value) {
        setCategoryId(value);
        return this;
    }

    @Override
    public BookRecord value11(Integer value) {
        setTotalCopies(value);
        return this;
    }

    @Override
    public BookRecord value12(Integer value) {
        setAvailableCopies(value);
        return this;
    }

    @Override
    public BookRecord value13(LocalDateTime value) {
        setCreatedTime(value);
        return this;
    }

    @Override
    public BookRecord value14(LocalDateTime value) {
        setUpdatedTime(value);
        return this;
    }

    @Override
    public BookRecord values(Long value1, String value2, String value3, String value4, String value5, LocalDate value6, BigDecimal value7, String value8, String value9, Long value10, Integer value11, Integer value12, LocalDateTime value13, LocalDateTime value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached BookRecord
     */
    public BookRecord() {
        super(Book.BOOK);
    }

    /**
     * Create a detached, initialised BookRecord
     */
    public BookRecord(Long id, String isbn, String title, String author, String publisher, LocalDate publishDate, BigDecimal price, String description, String coverUrl, Long categoryId, Integer totalCopies, Integer availableCopies, LocalDateTime createdTime, LocalDateTime updatedTime) {
        super(Book.BOOK);

        setId(id);
        setIsbn(isbn);
        setTitle(title);
        setAuthor(author);
        setPublisher(publisher);
        setPublishDate(publishDate);
        setPrice(price);
        setDescription(description);
        setCoverUrl(coverUrl);
        setCategoryId(categoryId);
        setTotalCopies(totalCopies);
        setAvailableCopies(availableCopies);
        setCreatedTime(createdTime);
        setUpdatedTime(updatedTime);
    }
}
