package com.library.management.config;

import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.conf.Settings;
import org.jooq.impl.DefaultDSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * jOOQ DSLContext配置类
 */
@Configuration
public class DataSourceConfig {

    /**
     * 创建DSLContext bean，这是jOOQ的核心API
     * 直接使用DataSource，不再创建TransactionAwareDataSourceProxy
     *
     * @param dataSource 注入的Spring数据源
     * @param settings jOOQ设置
     * @return jOOQ DSL上下文
     */
    @Bean
    @Primary
    public DSLContext dslContext(DataSource dataSource, Settings settings) {
        DefaultDSLContext dslContext = new DefaultDSLContext(dataSource, SQLDialect.MYSQL);
        dslContext.configuration().set(settings);
        return dslContext;
    }
} 