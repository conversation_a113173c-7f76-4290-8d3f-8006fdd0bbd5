/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated;


import com.library.management.model.generated.tables.Book;
import com.library.management.model.generated.tables.BorrowRecord;
import com.library.management.model.generated.tables.Category;
import com.library.management.model.generated.tables.User;
import com.library.management.model.generated.tables.records.BookRecord;
import com.library.management.model.generated.tables.records.BorrowRecordRecord;
import com.library.management.model.generated.tables.records.CategoryRecord;
import com.library.management.model.generated.tables.records.UserRecord;

import org.jooq.ForeignKey;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in
 * library_management.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<BookRecord> KEY_BOOK_IDX_ISBN = Internal.createUniqueKey(Book.BOOK, DSL.name("KEY_book_idx_isbn"), new TableField[] { Book.BOOK.ISBN }, true);
    public static final UniqueKey<BookRecord> KEY_BOOK_PRIMARY = Internal.createUniqueKey(Book.BOOK, DSL.name("KEY_book_PRIMARY"), new TableField[] { Book.BOOK.ID }, true);
    public static final UniqueKey<BorrowRecordRecord> KEY_BORROW_RECORD_PRIMARY = Internal.createUniqueKey(BorrowRecord.BORROW_RECORD, DSL.name("KEY_borrow_record_PRIMARY"), new TableField[] { BorrowRecord.BORROW_RECORD.ID }, true);
    public static final UniqueKey<CategoryRecord> KEY_CATEGORY_PRIMARY = Internal.createUniqueKey(Category.CATEGORY, DSL.name("KEY_category_PRIMARY"), new TableField[] { Category.CATEGORY.ID }, true);
    public static final UniqueKey<UserRecord> KEY_USER_IDX_EMAIL = Internal.createUniqueKey(User.USER, DSL.name("KEY_user_idx_email"), new TableField[] { User.USER.EMAIL }, true);
    public static final UniqueKey<UserRecord> KEY_USER_IDX_USERNAME = Internal.createUniqueKey(User.USER, DSL.name("KEY_user_idx_username"), new TableField[] { User.USER.USERNAME }, true);
    public static final UniqueKey<UserRecord> KEY_USER_PRIMARY = Internal.createUniqueKey(User.USER, DSL.name("KEY_user_PRIMARY"), new TableField[] { User.USER.ID }, true);

    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------

    public static final ForeignKey<BookRecord, CategoryRecord> BOOK_IBFK_1 = Internal.createForeignKey(Book.BOOK, DSL.name("book_ibfk_1"), new TableField[] { Book.BOOK.CATEGORY_ID }, Keys.KEY_CATEGORY_PRIMARY, new TableField[] { Category.CATEGORY.ID }, true);
    public static final ForeignKey<BorrowRecordRecord, UserRecord> BORROW_RECORD_IBFK_1 = Internal.createForeignKey(BorrowRecord.BORROW_RECORD, DSL.name("borrow_record_ibfk_1"), new TableField[] { BorrowRecord.BORROW_RECORD.USER_ID }, Keys.KEY_USER_PRIMARY, new TableField[] { User.USER.ID }, true);
    public static final ForeignKey<BorrowRecordRecord, BookRecord> BORROW_RECORD_IBFK_2 = Internal.createForeignKey(BorrowRecord.BORROW_RECORD, DSL.name("borrow_record_ibfk_2"), new TableField[] { BorrowRecord.BORROW_RECORD.BOOK_ID }, Keys.KEY_BOOK_PRIMARY, new TableField[] { Book.BOOK.ID }, true);
    public static final ForeignKey<CategoryRecord, CategoryRecord> CATEGORY_IBFK_1 = Internal.createForeignKey(Category.CATEGORY, DSL.name("category_ibfk_1"), new TableField[] { Category.CATEGORY.PARENT_ID }, Keys.KEY_CATEGORY_PRIMARY, new TableField[] { Category.CATEGORY.ID }, true);
}
