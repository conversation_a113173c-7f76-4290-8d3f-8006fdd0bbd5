package com.library.management.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 图书实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Book {
    /**
     * 图书ID
     */
    private Long id;
    
    /**
     * ISBN
     */
    private String isbn;
    
    /**
     * 书名
     */
    private String title;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 出版社
     */
    private String publisher;
    
    /**
     * 出版日期
     */
    private LocalDate publishDate;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 图书简介
     */
    private String description;
    
    /**
     * 封面图片URL
     */
    private String coverUrl;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 总藏书数量
     */
    private Integer totalCopies;
    
    /**
     * 可借数量
     */
    private Integer availableCopies;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
} 