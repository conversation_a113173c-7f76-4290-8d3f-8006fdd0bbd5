package com.library.management.repository;

import com.library.management.model.entity.BorrowRecord;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.impl.DSL.*;

/**
 * 借阅记录数据访问层
 */
@Repository
public class BorrowRepository {

    private final DSLContext dsl;

    @Autowired
    public BorrowRepository(DSLContext dsl) {
        this.dsl = dsl;
    }

    /**
     * 查找所有借阅记录
     *
     * @return 借阅记录列表
     */
    public List<BorrowRecord> findAll() {
        return dsl.select()
                .from("borrow_record")
                .orderBy(field("id").desc())
                .fetchInto(BorrowRecord.class);
    }

    /**
     * 根据ID查找借阅记录
     *
     * @param id 借阅记录ID
     * @return 借阅记录（可能为空）
     */
    public Optional<BorrowRecord> findById(Long id) {
        var result = dsl.select()
                .from("borrow_record")
                .where(field("id").eq(id))
                .fetchOneInto(BorrowRecord.class);

        return Optional.ofNullable(result);
    }

    /**
     * 创建借阅记录
     *
     * @param borrowRecord 借阅记录信息
     * @return 创建后的借阅记录（包含ID）
     */
    public BorrowRecord save(BorrowRecord borrowRecord) {
        if (borrowRecord.getId() == null) {
            // 新增借阅记录
            var record = dsl.insertInto(
                            table("borrow_record"),
                            field("user_id"),
                            field("book_id"),
                            field("borrow_date"),
                            field("due_date"),
                            field("status"),
                            field("renew_count"),
                            field("remarks")
                    )
                    .values(
                            borrowRecord.getUserId(),
                            borrowRecord.getBookId(),
                            borrowRecord.getBorrowDate(),
                            borrowRecord.getDueDate(),
                            borrowRecord.getStatus(),
                            borrowRecord.getRenewCount(),
                            borrowRecord.getRemarks()
                    )
                    .returning()
                    .fetchOne();

            if (record != null) {
                borrowRecord.setId(record.getValue(field("id", Long.class)));
            }
        } else {
            // 更新借阅记录
            dsl.update(table("borrow_record"))
                    .set(field("user_id"), borrowRecord.getUserId())
                    .set(field("book_id"), borrowRecord.getBookId())
                    .set(field("borrow_date"), borrowRecord.getBorrowDate())
                    .set(field("due_date"), borrowRecord.getDueDate())
                    .set(field("return_date"), borrowRecord.getReturnDate())
                    .set(field("status"), borrowRecord.getStatus())
                    .set(field("renew_count"), borrowRecord.getRenewCount())
                    .set(field("remarks"), borrowRecord.getRemarks())
                    .where(field("id").eq(borrowRecord.getId()))
                    .execute();
        }

        return borrowRecord;
    }

    /**
     * 更新归还日期和状态
     *
     * @param id         借阅记录ID
     * @param returnDate 归还日期
     * @param status     借阅状态
     * @return 是否更新成功
     */
    public boolean updateReturnInfo(Long id, LocalDateTime returnDate, Integer status) {
        return dsl.update(table("borrow_record"))
                .set(field("return_date"), returnDate)
                .set(field("status"), status)
                .where(field("id").eq(id))
                .execute() > 0;
    }

    /**
     * 更新续借信息
     *
     * @param id          借阅记录ID
     * @param newDueDate  新的应还日期
     * @param renewCount  续借次数
     * @return 是否更新成功
     */
    public boolean updateRenewInfo(Long id, LocalDateTime newDueDate, Integer renewCount) {
        return dsl.update(table("borrow_record"))
                .set(field("due_date"), newDueDate)
                .set(field("renew_count"), renewCount)
                .where(field("id").eq(id))
                .execute() > 0;
    }

    /**
     * 查找用户的借阅记录
     *
     * @param userId 用户ID
     * @return 借阅记录列表
     */
    public List<BorrowRecord> findByUserId(Long userId) {
        return dsl.select()
                .from("borrow_record")
                .where(field("user_id").eq(userId))
                .orderBy(field("borrow_date").desc())
                .fetchInto(BorrowRecord.class);
    }

    /**
     * 查找图书的借阅记录
     *
     * @param bookId 图书ID
     * @return 借阅记录列表
     */
    public List<BorrowRecord> findByBookId(Long bookId) {
        return dsl.select()
                .from("borrow_record")
                .where(field("book_id").eq(bookId))
                .orderBy(field("borrow_date").desc())
                .fetchInto(BorrowRecord.class);
    }

    /**
     * 查找用户当前的借阅记录（未归还）
     *
     * @param userId 用户ID
     * @return 借阅记录列表
     */
    public List<BorrowRecord> findCurrentBorrows(Long userId) {
        return dsl.select()
                .from("borrow_record")
                .where(field("user_id").eq(userId))
                .and(field("status").eq(1)) // 1-借出状态
                .orderBy(field("due_date"))
                .fetchInto(BorrowRecord.class);
    }

    /**
     * 查找逾期的借阅记录
     *
     * @return 借阅记录列表
     */
    public List<BorrowRecord> findOverdueRecords() {
        LocalDateTime now = LocalDateTime.now();
        return dsl.select()
                .from("borrow_record")
                .where(field("status").eq(1)) // 1-借出状态
                .and(field("due_date").lt(now))
                .orderBy(field("due_date"))
                .fetchInto(BorrowRecord.class);
    }
} 