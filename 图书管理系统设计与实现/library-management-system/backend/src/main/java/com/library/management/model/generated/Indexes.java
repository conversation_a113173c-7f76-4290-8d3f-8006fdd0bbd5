/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated;


import com.library.management.model.generated.tables.Book;
import com.library.management.model.generated.tables.BorrowRecord;
import com.library.management.model.generated.tables.Category;

import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables in library_management.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index BOOK_CATEGORY_ID = Internal.createIndex(DSL.name("category_id"), Book.BOOK, new OrderField[] { Book.BOOK.CATEGORY_ID }, false);
    public static final Index BOOK_IDX_AUTHOR = Internal.createIndex(DSL.name("idx_author"), Book.BOOK, new OrderField[] { Book.BOOK.AUTHOR }, false);
    public static final Index BORROW_RECORD_IDX_BOOK_ID = Internal.createIndex(DSL.name("idx_book_id"), BorrowRecord.BORROW_RECORD, new OrderField[] { BorrowRecord.BORROW_RECORD.BOOK_ID }, false);
    public static final Index BORROW_RECORD_IDX_STATUS = Internal.createIndex(DSL.name("idx_status"), BorrowRecord.BORROW_RECORD, new OrderField[] { BorrowRecord.BORROW_RECORD.STATUS }, false);
    public static final Index BOOK_IDX_TITLE = Internal.createIndex(DSL.name("idx_title"), Book.BOOK, new OrderField[] { Book.BOOK.TITLE }, false);
    public static final Index BORROW_RECORD_IDX_USER_ID = Internal.createIndex(DSL.name("idx_user_id"), BorrowRecord.BORROW_RECORD, new OrderField[] { BorrowRecord.BORROW_RECORD.USER_ID }, false);
    public static final Index CATEGORY_PARENT_ID = Internal.createIndex(DSL.name("parent_id"), Category.CATEGORY, new OrderField[] { Category.CATEGORY.PARENT_ID }, false);
}
