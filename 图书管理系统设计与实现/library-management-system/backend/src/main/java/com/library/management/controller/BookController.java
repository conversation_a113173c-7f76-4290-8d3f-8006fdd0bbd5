package com.library.management.controller;

import com.library.management.model.common.ApiResponse;
import com.library.management.model.entity.Book;
import com.library.management.model.entity.Category;
import com.library.management.service.BookService;
import com.library.management.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 图书控制器
 */
@RestController
@RequestMapping("/books")
public class BookController {

    private final BookService bookService;
    private final CategoryService categoryService;

    @Autowired
    public BookController(BookService bookService, CategoryService categoryService) {
        this.bookService = bookService;
        this.categoryService = categoryService;
    }

    /**
     * 获取图书列表
     */
    @GetMapping
    public ApiResponse<List<Book>> getBooks(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String author,
            @RequestParam(required = false) Long categoryId) {
        
        try {
            // 如果提供了查询参数，则搜索图书
            if (title != null || author != null || categoryId != null) {
                List<Book> books = bookService.searchBooks(title, author, categoryId);
                return ApiResponse.success(books);
            }
            
            // 否则返回所有图书
            List<Book> books = bookService.getAllBooks();
            return ApiResponse.success(books);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取图书列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定图书
     */
    @GetMapping("/{id}")
    public ApiResponse<Book> getBookById(@PathVariable Long id) {
        try {
            Optional<Book> bookOpt = bookService.getBookById(id);
            if (bookOpt.isPresent()) {
                return ApiResponse.success(bookOpt.get());
            } else {
                return ApiResponse.error(404, "图书不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "获取图书信息失败: " + e.getMessage());
        }
    }

    /**
     * 添加新图书
     */
    @PostMapping
    public ApiResponse<Book> addBook(@RequestBody Book book) {
        try {
            Book addedBook = bookService.addBook(book);
            return ApiResponse.success(addedBook);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "添加图书失败: " + e.getMessage());
        }
    }

    /**
     * 更新图书信息
     */
    @PutMapping("/{id}")
    public ApiResponse<Book> updateBook(@PathVariable Long id, @RequestBody Book book) {
        try {
            Book updatedBook = bookService.updateBook(id, book);
            return ApiResponse.success(updatedBook);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "更新图书失败: " + e.getMessage());
        }
    }

    /**
     * 删除图书
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteBook(@PathVariable Long id) {
        try {
            boolean success = bookService.deleteBook(id);
            if (success) {
                return ApiResponse.success();
            } else {
                return ApiResponse.error(404, "图书不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "删除图书失败: " + e.getMessage());
        }
    }

    /**
     * 获取图书分类
     */
    @GetMapping("/categories")
    public ApiResponse<List<String>> getCategories() {
        try {
            List<Category> categories = categoryService.getAllCategories();
            List<String> categoryNames = categories.stream()
                    .map(Category::getCategoryName)
                    .collect(Collectors.toList());
            return ApiResponse.success(categoryNames);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取图书分类失败: " + e.getMessage());
        }
    }
} 