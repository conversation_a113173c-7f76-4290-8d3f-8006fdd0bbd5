package com.library.management.controller;

import com.library.management.model.common.ApiResponse;
import com.library.management.model.entity.Book;
import com.library.management.service.BookService;
import com.library.management.service.BorrowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计分析控制器
 */
@RestController
@RequestMapping("/stats")
public class StatsController {
    
    private final BookService bookService;
    private final BorrowService borrowService;
    
    @Autowired
    public StatsController(BookService bookService, BorrowService borrowService) {
        this.bookService = bookService;
        this.borrowService = borrowService;
    }

    /**
     * 获取借阅统计数据
     */
    @GetMapping("/borrows")
    public ApiResponse<Map<String, Object>> getBorrowStats(
            @RequestParam(required = false) String period) {
        
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 由于目前没有真实的统计服务，这里返回模拟数据
            // 实际项目中应该调用统计服务来获取实际数据
            
            if ("day".equals(period)) {
                // 日借阅量统计
                List<Map<String, Object>> dailyStats = new ArrayList<>();
                LocalDate today = LocalDate.now();
                
                for (int i = 6; i >= 0; i--) {
                    LocalDate date = today.minusDays(i);
                    Map<String, Object> dayStat = new HashMap<>();
                    dayStat.put("date", date.format(DateTimeFormatter.ISO_DATE));
                    // 模拟数据，实际应该从数据库统计
                    dayStat.put("count", new Random().nextInt(20) + 10);
                    dailyStats.add(dayStat);
                }
                stats.put("dailyStats", dailyStats);
            } else if ("month".equals(period)) {
                // 月借阅量统计
                List<Map<String, Object>> monthlyStats = new ArrayList<>();
                LocalDate today = LocalDate.now();
                
                for (int i = 5; i >= 0; i--) {
                    LocalDate date = today.withDayOfMonth(1).minusMonths(i);
                    Map<String, Object> monthStat = new HashMap<>();
                    monthStat.put("month", date.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                    // 模拟数据，实际应该从数据库统计
                    monthStat.put("count", new Random().nextInt(200) + 100);
                    monthlyStats.add(monthStat);
                }
                stats.put("monthlyStats", monthlyStats);
            } else {
                // 分类借阅量统计
                List<Map<String, Object>> categoryStats = new ArrayList<>();
                // 获取所有图书，按分类ID分组并计数
                Map<Long, Long> categoryCounts = new HashMap<>();
                
                // 实际项目中应该从借阅记录统计各分类的借阅量
                // 这里使用模拟数据
                categoryCounts.put(1L, (long)(new Random().nextInt(50) + 20));
                categoryCounts.put(2L, (long)(new Random().nextInt(50) + 20));
                categoryCounts.put(3L, (long)(new Random().nextInt(50) + 20));
                categoryCounts.put(4L, (long)(new Random().nextInt(50) + 20));
                categoryCounts.put(5L, (long)(new Random().nextInt(50) + 20));
                
                for (Map.Entry<Long, Long> entry : categoryCounts.entrySet()) {
                    Map<String, Object> categoryStat = new HashMap<>();
                    categoryStat.put("categoryId", entry.getKey());
                    categoryStat.put("count", entry.getValue());
                    categoryStats.add(categoryStat);
                }
                stats.put("categoryStats", categoryStats);
            }
            
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取借阅统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门图书排行
     */
    @GetMapping("/books/popular")
    public ApiResponse<List<Map<String, Object>>> getPopularBooks(
            @RequestParam(required = false) String timeRange) {
        
        try {
            // 获取所有图书
            List<Book> allBooks = bookService.getAllBooks();
            
            // 由于目前没有真实的统计服务，这里返回模拟数据
            // 实际项目中应该基于借阅记录统计热门图书
            
            // 控制返回的热门图书数量
            int count = ("week".equals(timeRange)) ? 5 : ("month".equals(timeRange)) ? 10 : 15;
            count = Math.min(count, allBooks.size());
            
            List<Map<String, Object>> popularBooks = new ArrayList<>();
            
            // 随机选择一些图书作为热门图书
            Collections.shuffle(allBooks);
            for (int i = 0; i < count && i < allBooks.size(); i++) {
                Book book = allBooks.get(i);
                Map<String, Object> bookInfo = new HashMap<>();
                bookInfo.put("id", book.getId());
                bookInfo.put("title", book.getTitle());
                bookInfo.put("author", book.getAuthor());
                bookInfo.put("isbn", book.getIsbn());
                // 模拟借阅次数
                bookInfo.put("borrowCount", count * 2 - i + new Random().nextInt(5));
                popularBooks.add(bookInfo);
            }
            
            // 按借阅次数排序
            popularBooks.sort((b1, b2) -> {
                Integer count1 = (Integer) b1.get("borrowCount");
                Integer count2 = (Integer) b2.get("borrowCount");
                return count2.compareTo(count1);
            });
            
            return ApiResponse.success(popularBooks);
            
        } catch (Exception e) {
            return ApiResponse.error(500, "获取热门图书失败: " + e.getMessage());
        }
    }

    /**
     * 导出统计报表
     */
    @GetMapping("/reports/export")
    public ApiResponse<String> exportReport(
            @RequestParam String type,
            @RequestParam(required = false) String timeRange) {
        
        try {
            // 实际项目中应该生成Excel文件并提供下载链接
            // 这里仅返回一个模拟的文件名
            
            String fileName = "library_stats_" + type + "_" + 
                     (timeRange != null ? timeRange + "_" : "") + 
                     System.currentTimeMillis() + ".xlsx";
            
            return ApiResponse.success("报表已生成，文件名：" + fileName);
        } catch (Exception e) {
            return ApiResponse.error(500, "导出报表失败: " + e.getMessage());
        }
    }
} 