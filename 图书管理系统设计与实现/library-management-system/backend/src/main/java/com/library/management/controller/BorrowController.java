package com.library.management.controller;

import com.library.management.model.common.ApiResponse;
import com.library.management.model.entity.BorrowRecord;
import com.library.management.service.BorrowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Optional;

/**
 * 借阅记录控制器
 */
@RestController
@RequestMapping("/borrows")
public class BorrowController {

    private final BorrowService borrowService;

    @Autowired
    public BorrowController(BorrowService borrowService) {
        this.borrowService = borrowService;
    }

    /**
     * 获取所有借阅记录
     */
    @GetMapping
    public ApiResponse<List<BorrowRecord>> getBorrows() {
        try {
            List<BorrowRecord> borrows = borrowService.getAllBorrows();
            return ApiResponse.success(borrows);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取借阅记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定借阅记录
     */
    @GetMapping("/{id}")
    public ApiResponse<BorrowRecord> getBorrowById(@PathVariable Long id) {
        try {
            Optional<BorrowRecord> borrowOpt = borrowService.getBorrowById(id);
            if (borrowOpt.isPresent()) {
                return ApiResponse.success(borrowOpt.get());
            } else {
                return ApiResponse.error(404, "借阅记录不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "获取借阅记录失败: " + e.getMessage());
        }
    }

    /**
     * 借阅图书
     */
    @PostMapping
    public ApiResponse<BorrowRecord> borrowBook(@RequestBody BorrowRecord borrowRecord, HttpSession session) {
        try {
            // 从Session获取当前用户ID
            Long userId = getCurrentUserId(session);
            if (userId == null) {
                return ApiResponse.error(401, "未登录");
            }
            
            // 使用当前用户ID和请求中的图书ID创建借阅记录
            BorrowRecord borrow = borrowService.borrowBook(userId, borrowRecord.getBookId());
            return ApiResponse.success(borrow);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "借阅图书失败: " + e.getMessage());
        }
    }

    /**
     * 归还图书
     */
    @PostMapping("/{id}/return")
    public ApiResponse<BorrowRecord> returnBook(@PathVariable Long id, HttpSession session) {
        try {
            // 从Session获取当前用户ID
            Long userId = getCurrentUserId(session);
            if (userId == null) {
                return ApiResponse.error(401, "未登录");
            }
            
            // 检查是否是当前用户的借阅记录
            Optional<BorrowRecord> borrowOpt = borrowService.getBorrowById(id);
            if (borrowOpt.isEmpty()) {
                return ApiResponse.error(404, "借阅记录不存在");
            }
            
            BorrowRecord borrow = borrowOpt.get();
            if (!borrow.getUserId().equals(userId)) {
                return ApiResponse.error(403, "无权操作此借阅记录");
            }
            
            // 归还图书
            BorrowRecord returnedBorrow = borrowService.returnBook(id);
            return ApiResponse.success(returnedBorrow);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "归还图书失败: " + e.getMessage());
        }
    }

    /**
     * 续借图书
     */
    @PostMapping("/{id}/renew")
    public ApiResponse<BorrowRecord> renewBook(@PathVariable Long id, HttpSession session) {
        try {
            // 从Session获取当前用户ID
            Long userId = getCurrentUserId(session);
            if (userId == null) {
                return ApiResponse.error(401, "未登录");
            }
            
            // 检查是否是当前用户的借阅记录
            Optional<BorrowRecord> borrowOpt = borrowService.getBorrowById(id);
            if (borrowOpt.isEmpty()) {
                return ApiResponse.error(404, "借阅记录不存在");
            }
            
            BorrowRecord borrow = borrowOpt.get();
            if (!borrow.getUserId().equals(userId)) {
                return ApiResponse.error(403, "无权操作此借阅记录");
            }
            
            // 续借图书
            BorrowRecord renewedBorrow = borrowService.renewBook(id);
            return ApiResponse.success(renewedBorrow);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "续借图书失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的借阅记录
     */
    @GetMapping("/user/{userId}")
    public ApiResponse<List<BorrowRecord>> getUserBorrows(@PathVariable Long userId) {
        try {
            List<BorrowRecord> borrows = borrowService.getBorrowsByUserId(userId);
            return ApiResponse.success(borrows);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "获取用户借阅记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户的借阅记录
     */
    @GetMapping("/my")
    public ApiResponse<List<BorrowRecord>> getMyBorrows(HttpSession session) {
        try {
            // 从Session获取当前用户ID
            Long userId = getCurrentUserId(session);
            if (userId == null) {
                return ApiResponse.error(401, "未登录");
            }
            
            List<BorrowRecord> borrows = borrowService.getBorrowsByUserId(userId);
            return ApiResponse.success(borrows);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取借阅记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取逾期的借阅记录
     */
    @GetMapping("/overdue")
    public ApiResponse<List<BorrowRecord>> getOverdueRecords() {
        try {
            List<BorrowRecord> borrows = borrowService.getOverdueRecords();
            return ApiResponse.success(borrows);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取逾期记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 从Session中获取当前用户ID
     */
    private Long getCurrentUserId(HttpSession session) {
        Object userObj = session.getAttribute("currentUser");
        if (userObj != null && userObj instanceof com.library.management.model.entity.User) {
            return ((com.library.management.model.entity.User) userObj).getId();
        }
        return null;
    }
} 