package com.library.management.service;

import com.library.management.model.entity.Category;
import com.library.management.repository.CategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 图书分类服务
 */
@Service
public class CategoryService {

    private final CategoryRepository categoryRepository;

    @Autowired
    public CategoryService(CategoryRepository categoryRepository) {
        this.categoryRepository = categoryRepository;
    }

    /**
     * 获取所有分类
     *
     * @return 分类列表
     */
    public List<Category> getAllCategories() {
        return categoryRepository.findAll();
    }

    /**
     * 根据ID查找分类
     *
     * @param id 分类ID
     * @return 分类信息（可能为空）
     */
    public Optional<Category> getCategoryById(Long id) {
        return categoryRepository.findById(id);
    }

    /**
     * 添加新分类
     *
     * @param category 分类信息
     * @return 添加后的分类（包含ID）
     * @throws IllegalArgumentException 如果分类名称已存在或父分类不存在
     */
    public Category addCategory(Category category) {
        // 检查分类名称是否已存在
        Optional<Category> existingCategory = categoryRepository.findByCategoryName(category.getCategoryName());
        if (existingCategory.isPresent()) {
            throw new IllegalArgumentException("分类名称已存在");
        }

        // 如果指定了父分类，检查父分类是否存在
        if (category.getParentId() != null) {
            if (categoryRepository.findById(category.getParentId()).isEmpty()) {
                throw new IllegalArgumentException("父分类不存在");
            }
        }

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        category.setCreatedTime(now);
        category.setUpdatedTime(now);

        return categoryRepository.save(category);
    }

    /**
     * 更新分类信息
     *
     * @param id       分类ID
     * @param category 更新的分类信息
     * @return 更新后的分类信息
     * @throws IllegalArgumentException 如果分类不存在，分类名称已被其他分类使用，或父分类不存在
     */
    public Category updateCategory(Long id, Category category) {
        // 确保分类存在
        Optional<Category> existingCategoryOpt = categoryRepository.findById(id);
        if (existingCategoryOpt.isEmpty()) {
            throw new IllegalArgumentException("分类不存在");
        }

        Category existingCategory = existingCategoryOpt.get();

        // 如果更新分类名称，检查是否与其他分类冲突
        if (!existingCategory.getCategoryName().equals(category.getCategoryName())) {
            Optional<Category> conflictCategory = categoryRepository.findByCategoryName(category.getCategoryName());
            if (conflictCategory.isPresent() && !conflictCategory.get().getId().equals(id)) {
                throw new IllegalArgumentException("分类名称已被其他分类使用");
            }
        }

        // 如果指定了父分类，检查父分类是否存在且不是自身
        if (category.getParentId() != null) {
            if (category.getParentId().equals(id)) {
                throw new IllegalArgumentException("分类不能作为自己的父分类");
            }
            if (categoryRepository.findById(category.getParentId()).isEmpty()) {
                throw new IllegalArgumentException("父分类不存在");
            }
        }

        // 设置ID和时间
        category.setId(id);
        category.setCreatedTime(existingCategory.getCreatedTime());
        category.setUpdatedTime(LocalDateTime.now());

        return categoryRepository.save(category);
    }

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 是否删除成功
     */
    public boolean deleteCategory(Long id) {
        // 检查分类是否存在
        if (categoryRepository.findById(id).isEmpty()) {
            return false;
        }

        // 检查是否有子分类
        List<Category> children = categoryRepository.findByParentId(id);
        if (!children.isEmpty()) {
            throw new IllegalArgumentException("该分类有子分类，无法删除");
        }

        return categoryRepository.deleteById(id);
    }

    /**
     * 获取顶级分类（无父分类）
     *
     * @return 顶级分类列表
     */
    public List<Category> getTopCategories() {
        return categoryRepository.findTopCategories();
    }

    /**
     * 获取子分类
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    public List<Category> getChildCategories(Long parentId) {
        // 检查父分类是否存在
        if (categoryRepository.findById(parentId).isEmpty()) {
            throw new IllegalArgumentException("父分类不存在");
        }

        return categoryRepository.findByParentId(parentId);
    }
} 