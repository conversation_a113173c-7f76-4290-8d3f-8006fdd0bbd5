package com.library.management.service;

import com.library.management.model.entity.User;
import com.library.management.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户服务
 */
@Service
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * 用户登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @return 用户信息（如果验证成功）
     */
    public Optional<User> login(String username, String password) {
        // 查找用户
        Optional<User> userOpt = userRepository.findByUsername(username);
        
        // 验证用户存在且密码匹配
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            
            // 使用 BCrypt 验证密码
            if (passwordEncoder.matches(password, user.getPassword())) {
                // 登录成功，清除密码返回用户信息
                user.setPassword(null);
                return Optional.of(user);
            }
        }
        
        return Optional.empty();
    }

    /**
     * 用户注册
     *
     * @param user 用户信息
     * @return 注册后的用户信息
     * @throws IllegalArgumentException 如果用户名已存在
     */
    public User register(User user) {
        // 检查用户名是否已存在
        if (userRepository.findByUsername(user.getUsername()).isPresent()) {
            throw new IllegalArgumentException("用户名已存在");
        }
        
        // 设置默认值
        if (user.getRole() == null) {
            user.setRole(1); // 默认为普通用户
        }
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认为启用状态
        }
        
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        user.setCreatedTime(now);
        user.setUpdatedTime(now);
        
        // 对密码进行加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 保存用户
        User savedUser = userRepository.save(user);
        
        // 清除密码后返回
        savedUser.setPassword(null);
        return savedUser;
    }

    /**
     * 获取所有用户
     *
     * @return 用户列表
     */
    public List<User> getAllUsers() {
        List<User> users = userRepository.findAll();
        // 清除所有用户的密码字段
        users.forEach(user -> user.setPassword(null));
        return users;
    }

    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 用户信息（可能为空）
     */
    public Optional<User> getUserById(Long id) {
        Optional<User> userOpt = userRepository.findById(id);
        // 清除密码
        userOpt.ifPresent(user -> user.setPassword(null));
        return userOpt;
    }

    /**
     * 更新用户信息
     *
     * @param id   用户ID
     * @param user 更新的用户信息
     * @return 更新后的用户信息
     * @throws IllegalArgumentException 如果用户不存在
     */
    public User updateUser(Long id, User user) {
        // 确保用户存在
        Optional<User> existingUserOpt = userRepository.findById(id);
        if (existingUserOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        User existingUser = existingUserOpt.get();
        
        // 如果更新用户名，检查是否与其他用户冲突
        if (!existingUser.getUsername().equals(user.getUsername())) {
            Optional<User> conflictUser = userRepository.findByUsername(user.getUsername());
            if (conflictUser.isPresent() && !conflictUser.get().getId().equals(id)) {
                throw new IllegalArgumentException("用户名已被其他用户使用");
            }
        }
        
        // 设置不变的字段
        user.setId(id);
        user.setPassword(existingUser.getPassword()); // 不通过此方法更改密码
        user.setCreatedTime(existingUser.getCreatedTime());
        user.setUpdatedTime(LocalDateTime.now());
        
        // 保存更新
        User savedUser = userRepository.save(user);
        
        // 清除密码后返回
        savedUser.setPassword(null);
        return savedUser;
    }

    /**
     * 更新用户密码
     *
     * @param id          用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否更新成功
     */
    public boolean updatePassword(Long id, String oldPassword, String newPassword) {
        // 检查用户是否存在
        Optional<User> userOpt = userRepository.findById(id);
        if (userOpt.isEmpty()) {
            return false;
        }
        
        User user = userOpt.get();
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            return false;
        }
        
        // 加密新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        
        // 更新密码
        return userRepository.updatePassword(id, encodedPassword);
    }

    /**
     * 停用/启用用户
     *
     * @param id     用户ID
     * @param status 新状态（0-禁用，1-启用）
     * @return 是否更新成功
     */
    public boolean updateUserStatus(Long id, Integer status) {
        // 检查用户是否存在
        if (userRepository.findById(id).isEmpty()) {
            return false;
        }
        
        return userRepository.updateStatus(id, status);
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否删除成功
     */
    public boolean deleteUser(Long id) {
        // 检查用户是否存在
        if (userRepository.findById(id).isEmpty()) {
            return false;
        }
        
        return userRepository.deleteById(id);
    }
} 