/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated;


import com.library.management.model.generated.tables.Book;
import com.library.management.model.generated.tables.BorrowRecord;
import com.library.management.model.generated.tables.Category;
import com.library.management.model.generated.tables.User;

import java.util.Arrays;
import java.util.List;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LibraryManagement extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>library_management</code>
     */
    public static final LibraryManagement LIBRARY_MANAGEMENT = new LibraryManagement();

    /**
     * 图书表
     */
    public final Book BOOK = Book.BOOK;

    /**
     * 借阅记录表
     */
    public final BorrowRecord BORROW_RECORD = BorrowRecord.BORROW_RECORD;

    /**
     * 图书分类表
     */
    public final Category CATEGORY = Category.CATEGORY;

    /**
     * 用户表
     */
    public final User USER = User.USER;

    /**
     * No further instances allowed
     */
    private LibraryManagement() {
        super("library_management", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.asList(
            Book.BOOK,
            BorrowRecord.BORROW_RECORD,
            Category.CATEGORY,
            User.USER
        );
    }
}
