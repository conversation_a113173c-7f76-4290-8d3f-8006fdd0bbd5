/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated;


import com.library.management.model.generated.tables.Book;
import com.library.management.model.generated.tables.BorrowRecord;
import com.library.management.model.generated.tables.Category;
import com.library.management.model.generated.tables.User;


/**
 * Convenience access to all tables in library_management.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * 图书表
     */
    public static final Book BOOK = Book.BOOK;

    /**
     * 借阅记录表
     */
    public static final BorrowRecord BORROW_RECORD = BorrowRecord.BORROW_RECORD;

    /**
     * 图书分类表
     */
    public static final Category CATEGORY = Category.CATEGORY;

    /**
     * 用户表
     */
    public static final User USER = User.USER;
}
