package com.library.management.controller;

import com.library.management.model.common.ApiResponse;
import com.library.management.model.entity.Category;
import com.library.management.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 图书分类控制器
 */
@RestController
@RequestMapping("/categories")
public class CategoryController {

    private final CategoryService categoryService;

    @Autowired
    public CategoryController(CategoryService categoryService) {
        this.categoryService = categoryService;
    }

    /**
     * 获取所有分类
     */
    @GetMapping
    public ApiResponse<List<Category>> getAllCategories() {
        try {
            List<Category> categories = categoryService.getAllCategories();
            return ApiResponse.success(categories);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定分类
     */
    @GetMapping("/{id}")
    public ApiResponse<Category> getCategoryById(@PathVariable Long id) {
        try {
            Optional<Category> categoryOpt = categoryService.getCategoryById(id);
            if (categoryOpt.isPresent()) {
                return ApiResponse.success(categoryOpt.get());
            } else {
                return ApiResponse.error(404, "分类不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "获取分类信息失败: " + e.getMessage());
        }
    }

    /**
     * 添加新分类
     */
    @PostMapping
    public ApiResponse<Category> addCategory(@RequestBody Category category) {
        try {
            Category addedCategory = categoryService.addCategory(category);
            return ApiResponse.success(addedCategory);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "添加分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新分类信息
     */
    @PutMapping("/{id}")
    public ApiResponse<Category> updateCategory(@PathVariable Long id, @RequestBody Category category) {
        try {
            Category updatedCategory = categoryService.updateCategory(id, category);
            return ApiResponse.success(updatedCategory);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "更新分类失败: " + e.getMessage());
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteCategory(@PathVariable Long id) {
        try {
            boolean success = categoryService.deleteCategory(id);
            if (success) {
                return ApiResponse.success();
            } else {
                return ApiResponse.error(404, "分类不存在");
            }
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "删除分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取顶级分类（无父分类）
     */
    @GetMapping("/top")
    public ApiResponse<List<Category>> getTopCategories() {
        try {
            List<Category> categories = categoryService.getTopCategories();
            return ApiResponse.success(categories);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取顶级分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取子分类
     */
    @GetMapping("/{parentId}/children")
    public ApiResponse<List<Category>> getChildCategories(@PathVariable Long parentId) {
        try {
            List<Category> categories = categoryService.getChildCategories(parentId);
            return ApiResponse.success(categories);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error(500, "获取子分类失败: " + e.getMessage());
        }
    }
} 