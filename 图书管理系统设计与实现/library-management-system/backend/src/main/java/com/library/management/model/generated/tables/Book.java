/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated.tables;


import com.library.management.model.generated.Indexes;
import com.library.management.model.generated.Keys;
import com.library.management.model.generated.LibraryManagement;
import com.library.management.model.generated.tables.records.BookRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * 图书表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Book extends TableImpl<BookRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>library_management.book</code>
     */
    public static final Book BOOK = new Book();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BookRecord> getRecordType() {
        return BookRecord.class;
    }

    /**
     * The column <code>library_management.book.id</code>.
     */
    public final TableField<BookRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

    /**
     * The column <code>library_management.book.isbn</code>.
     */
    public final TableField<BookRecord, String> ISBN = createField(DSL.name("isbn"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>library_management.book.title</code>.
     */
    public final TableField<BookRecord, String> TITLE = createField(DSL.name("title"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>library_management.book.author</code>.
     */
    public final TableField<BookRecord, String> AUTHOR = createField(DSL.name("author"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>library_management.book.publisher</code>.
     */
    public final TableField<BookRecord, String> PUBLISHER = createField(DSL.name("publisher"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>library_management.book.publish_date</code>.
     */
    public final TableField<BookRecord, LocalDate> PUBLISH_DATE = createField(DSL.name("publish_date"), SQLDataType.LOCALDATE, this, "");

    /**
     * The column <code>library_management.book.price</code>.
     */
    public final TableField<BookRecord, BigDecimal> PRICE = createField(DSL.name("price"), SQLDataType.DECIMAL(10, 2), this, "");

    /**
     * The column <code>library_management.book.description</code>.
     */
    public final TableField<BookRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>library_management.book.cover_url</code>.
     */
    public final TableField<BookRecord, String> COVER_URL = createField(DSL.name("cover_url"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>library_management.book.category_id</code>.
     */
    public final TableField<BookRecord, Long> CATEGORY_ID = createField(DSL.name("category_id"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>library_management.book.total_copies</code>.
     */
    public final TableField<BookRecord, Integer> TOTAL_COPIES = createField(DSL.name("total_copies"), SQLDataType.INTEGER.defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>library_management.book.available_copies</code>.
     */
    public final TableField<BookRecord, Integer> AVAILABLE_COPIES = createField(DSL.name("available_copies"), SQLDataType.INTEGER.defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>library_management.book.created_time</code>.
     */
    public final TableField<BookRecord, LocalDateTime> CREATED_TIME = createField(DSL.name("created_time"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "");

    /**
     * The column <code>library_management.book.updated_time</code>.
     */
    public final TableField<BookRecord, LocalDateTime> UPDATED_TIME = createField(DSL.name("updated_time"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "");

    private Book(Name alias, Table<BookRecord> aliased) {
        this(alias, aliased, null);
    }

    private Book(Name alias, Table<BookRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("图书表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>library_management.book</code> table reference
     */
    public Book(String alias) {
        this(DSL.name(alias), BOOK);
    }

    /**
     * Create an aliased <code>library_management.book</code> table reference
     */
    public Book(Name alias) {
        this(alias, BOOK);
    }

    /**
     * Create a <code>library_management.book</code> table reference
     */
    public Book() {
        this(DSL.name("book"), null);
    }

    public <O extends Record> Book(Table<O> child, ForeignKey<O, BookRecord> key) {
        super(child, key, BOOK);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : LibraryManagement.LIBRARY_MANAGEMENT;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.BOOK_CATEGORY_ID, Indexes.BOOK_IDX_AUTHOR, Indexes.BOOK_IDX_TITLE);
    }

    @Override
    public Identity<BookRecord, Long> getIdentity() {
        return (Identity<BookRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<BookRecord> getPrimaryKey() {
        return Keys.KEY_BOOK_PRIMARY;
    }

    @Override
    public List<UniqueKey<BookRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_BOOK_IDX_ISBN);
    }

    @Override
    public List<ForeignKey<BookRecord, ?>> getReferences() {
        return Arrays.asList(Keys.BOOK_IBFK_1);
    }

    private transient Category _category;

    /**
     * Get the implicit join path to the
     * <code>library_management.category</code> table.
     */
    public Category category() {
        if (_category == null)
            _category = new Category(this, Keys.BOOK_IBFK_1);

        return _category;
    }

    @Override
    public Book as(String alias) {
        return new Book(DSL.name(alias), this);
    }

    @Override
    public Book as(Name alias) {
        return new Book(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Book rename(String name) {
        return new Book(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Book rename(Name name) {
        return new Book(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<Long, String, String, String, String, LocalDate, BigDecimal, String, String, Long, Integer, Integer, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
