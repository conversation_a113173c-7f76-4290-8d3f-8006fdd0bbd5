/*
 * This file is generated by jOOQ.
 */
package com.library.management.model.generated.tables.records;


import com.library.management.model.generated.tables.BorrowRecord;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 借阅记录表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BorrowRecordRecord extends UpdatableRecordImpl<BorrowRecordRecord> implements Record9<Long, Long, Long, LocalDateTime, LocalDateTime, LocalDateTime, Byte, Integer, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>library_management.borrow_record.id</code>.
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>library_management.borrow_record.user_id</code>.
     */
    public void setUserId(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.user_id</code>.
     */
    public Long getUserId() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>library_management.borrow_record.book_id</code>.
     */
    public void setBookId(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.book_id</code>.
     */
    public Long getBookId() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>library_management.borrow_record.borrow_date</code>.
     */
    public void setBorrowDate(LocalDateTime value) {
        set(3, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.borrow_date</code>.
     */
    public LocalDateTime getBorrowDate() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>library_management.borrow_record.due_date</code>.
     */
    public void setDueDate(LocalDateTime value) {
        set(4, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.due_date</code>.
     */
    public LocalDateTime getDueDate() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>library_management.borrow_record.return_date</code>.
     */
    public void setReturnDate(LocalDateTime value) {
        set(5, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.return_date</code>.
     */
    public LocalDateTime getReturnDate() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>library_management.borrow_record.status</code>. 1-借出,
     * 2-已还, 3-逾期
     */
    public void setStatus(Byte value) {
        set(6, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.status</code>. 1-借出,
     * 2-已还, 3-逾期
     */
    public Byte getStatus() {
        return (Byte) get(6);
    }

    /**
     * Setter for <code>library_management.borrow_record.renew_count</code>.
     */
    public void setRenewCount(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.renew_count</code>.
     */
    public Integer getRenewCount() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>library_management.borrow_record.remarks</code>.
     */
    public void setRemarks(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>library_management.borrow_record.remarks</code>.
     */
    public String getRemarks() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<Long, Long, Long, LocalDateTime, LocalDateTime, LocalDateTime, Byte, Integer, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<Long, Long, Long, LocalDateTime, LocalDateTime, LocalDateTime, Byte, Integer, String> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return BorrowRecord.BORROW_RECORD.ID;
    }

    @Override
    public Field<Long> field2() {
        return BorrowRecord.BORROW_RECORD.USER_ID;
    }

    @Override
    public Field<Long> field3() {
        return BorrowRecord.BORROW_RECORD.BOOK_ID;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return BorrowRecord.BORROW_RECORD.BORROW_DATE;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return BorrowRecord.BORROW_RECORD.DUE_DATE;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return BorrowRecord.BORROW_RECORD.RETURN_DATE;
    }

    @Override
    public Field<Byte> field7() {
        return BorrowRecord.BORROW_RECORD.STATUS;
    }

    @Override
    public Field<Integer> field8() {
        return BorrowRecord.BORROW_RECORD.RENEW_COUNT;
    }

    @Override
    public Field<String> field9() {
        return BorrowRecord.BORROW_RECORD.REMARKS;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getUserId();
    }

    @Override
    public Long component3() {
        return getBookId();
    }

    @Override
    public LocalDateTime component4() {
        return getBorrowDate();
    }

    @Override
    public LocalDateTime component5() {
        return getDueDate();
    }

    @Override
    public LocalDateTime component6() {
        return getReturnDate();
    }

    @Override
    public Byte component7() {
        return getStatus();
    }

    @Override
    public Integer component8() {
        return getRenewCount();
    }

    @Override
    public String component9() {
        return getRemarks();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getUserId();
    }

    @Override
    public Long value3() {
        return getBookId();
    }

    @Override
    public LocalDateTime value4() {
        return getBorrowDate();
    }

    @Override
    public LocalDateTime value5() {
        return getDueDate();
    }

    @Override
    public LocalDateTime value6() {
        return getReturnDate();
    }

    @Override
    public Byte value7() {
        return getStatus();
    }

    @Override
    public Integer value8() {
        return getRenewCount();
    }

    @Override
    public String value9() {
        return getRemarks();
    }

    @Override
    public BorrowRecordRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value2(Long value) {
        setUserId(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value3(Long value) {
        setBookId(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value4(LocalDateTime value) {
        setBorrowDate(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value5(LocalDateTime value) {
        setDueDate(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value6(LocalDateTime value) {
        setReturnDate(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value7(Byte value) {
        setStatus(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value8(Integer value) {
        setRenewCount(value);
        return this;
    }

    @Override
    public BorrowRecordRecord value9(String value) {
        setRemarks(value);
        return this;
    }

    @Override
    public BorrowRecordRecord values(Long value1, Long value2, Long value3, LocalDateTime value4, LocalDateTime value5, LocalDateTime value6, Byte value7, Integer value8, String value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached BorrowRecordRecord
     */
    public BorrowRecordRecord() {
        super(BorrowRecord.BORROW_RECORD);
    }

    /**
     * Create a detached, initialised BorrowRecordRecord
     */
    public BorrowRecordRecord(Long id, Long userId, Long bookId, LocalDateTime borrowDate, LocalDateTime dueDate, LocalDateTime returnDate, Byte status, Integer renewCount, String remarks) {
        super(BorrowRecord.BORROW_RECORD);

        setId(id);
        setUserId(userId);
        setBookId(bookId);
        setBorrowDate(borrowDate);
        setDueDate(dueDate);
        setReturnDate(returnDate);
        setStatus(status);
        setRenewCount(renewCount);
        setRemarks(remarks);
    }
}
