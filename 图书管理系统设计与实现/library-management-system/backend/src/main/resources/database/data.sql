-- 使用数据库
USE library_management;

-- 添加示例用户数据
INSERT INTO `user` (`username`, `password`, `real_name`, `email`, `phone`, `role`, `status`) VALUES
('user1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '张三', '<EMAIL>', '13800000001', 1, 1),
('user2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '李四', '<EMAIL>', '13800000002', 1, 1),
('user3', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '王五', '<EMAIL>', '13800000003', 1, 1),
('librarian', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '图书管理员', '<EMAIL>', '13900000001', 2, 1)
ON DUPLICATE KEY UPDATE `username` = `username`;

-- 添加子分类
INSERT INTO `category` (`category_name`, `parent_id`, `description`) VALUES
('现代文学', 1, '现代文学作品'),
('古典文学', 1, '古典文学作品'),
('科幻小说', 1, '科幻类小说作品'),
('Java编程', 9, 'Java编程相关书籍'),
('Python编程', 9, 'Python编程相关书籍'),
('Web开发', 9, 'Web开发相关书籍'),
('数据库技术', 9, '数据库相关技术书籍'),
('人工智能', 2, '人工智能相关书籍'),
('中国历史', 3, '中国历史相关书籍'),
('世界历史', 3, '世界历史相关书籍')
ON DUPLICATE KEY UPDATE `category_name` = `category_name`;

-- 添加示例图书数据
INSERT INTO `book` (`isbn`, `title`, `author`, `publisher`, `publish_date`, `price`, `description`, `cover_url`, `category_id`, `total_copies`, `available_copies`) VALUES
('9787302392644', 'Java核心技术 卷I', 'Cay S. Horstmann', '机械工业出版社', '2016-01-01', 95.00, 'Java领域经典著作，全面介绍Java编程基础和核心技术。', '/images/covers/java-core-vol1.jpg', 13, 10, 8),
('9787115428028', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 89.00, '全面而通俗的Python编程入门书籍，适合初学者。', '/images/covers/python-crash-course.jpg', 14, 15, 12),
('9787115546081', 'Spring Boot实战', 'Craig Walls', '人民邮电出版社', '2019-04-01', 79.00, '介绍Spring Boot的使用和最佳实践。', '/images/covers/spring-boot-in-action.jpg', 15, 8, 5),
('9787111648727', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2020-07-01', 139.00, '计算机系统经典教材，介绍计算机硬件和软件系统的基本概念。', '/images/covers/csapp.jpg', 9, 5, 3),
('9787115571915', 'Python数据科学手册', 'Jake VanderPlas', '人民邮电出版社', '2019-05-01', 99.00, '介绍使用Python进行数据分析和科学计算的工具和方法。', '/images/covers/python-data-science.jpg', 14, 10, 8),
('9787121384561', '代码整洁之道', 'Robert C. Martin', '电子工业出版社', '2019-09-01', 69.00, '软件开发经典著作，教你如何编写干净、可维护的代码。', '/images/covers/clean-code.jpg', 13, 12, 10),
('9787549529100', '百年孤独', '加西亚·马尔克斯', '南海出版公司', '2017-06-01', 55.00, '加西亚·马尔克斯的代表作，魔幻现实主义文学经典。', '/images/covers/one-hundred-years.jpg', 2, 20, 15),
('9787544253994', '追风筝的人', '卡勒德·胡赛尼', '上海译文出版社', '2006-05-01', 29.00, '畅销全球的现代文学经典，讲述了友情、背叛和救赎的故事。', '/images/covers/the-kite-runner.jpg', 10, 18, 15),
('9787020116133', '平凡的世界', '路遥', '人民文学出版社', '2017-06-01', 128.00, '路遥的代表作，描绘了中国农村青年的奋斗历程。', '/images/covers/ordinary-world.jpg', 10, 15, 12),
('9787301214350', '中国大历史', '黄仁宇', '北京大学出版社', '2015-07-01', 42.00, '从宏观角度解析中国历史的演变规律。', '/images/covers/china-history.jpg', 18, 8, 6),
('9787513344982', '简明世界史', '房龙', '新星出版社', '2018-01-01', 68.00, '通俗易懂的世界历史读物，适合各年龄段的读者。', '/images/covers/world-history.jpg', 19, 10, 8),
('9787559627940', '人工智能简史', '尼克', '北京联合出版公司', '2019-10-01', 79.00, '介绍人工智能从起源到现在的发展历程。', '/images/covers/ai-history.jpg', 17, 10, 9),
('9787111636472', '深度学习', 'Ian Goodfellow', '机械工业出版社', '2017-08-01', 168.00, '人工智能领域经典教材，深入介绍深度学习理论和实践。', '/images/covers/deep-learning.jpg', 17, 6, 4),
('9787115519290', 'SQL必知必会', 'Ben Forta', '人民邮电出版社', '2019-01-01', 45.00, '简洁明了的SQL入门书籍，适合数据库初学者。', '/images/covers/sql-essentials.jpg', 16, 15, 12),
('9787111545682', '数据库系统概念', 'Abraham Silberschatz', '机械工业出版社', '2017-05-01', 129.00, '数据库领域的经典教材，全面介绍数据库系统的基本概念和实现技术。', '/images/covers/database-concepts.jpg', 16, 8, 5)
ON DUPLICATE KEY UPDATE `isbn` = `isbn`;

-- 添加借阅记录
INSERT INTO `borrow_record` (`user_id`, `book_id`, `borrow_date`, `due_date`, `return_date`, `status`, `renew_count`, `remarks`) VALUES
(2, 1, '2023-01-10 10:00:00', '2023-02-10 10:00:00', '2023-02-05 14:30:00', 2, 0, '按时归还'),
(2, 3, '2023-03-05 14:30:00', '2023-04-05 14:30:00', NULL, 1, 0, NULL),
(3, 2, '2023-02-15 16:00:00', '2023-03-15 16:00:00', '2023-03-30 11:20:00', 3, 1, '逾期归还'),
(3, 4, '2023-03-20 09:15:00', '2023-04-20 09:15:00', NULL, 1, 0, NULL),
(4, 5, '2023-02-28 13:45:00', '2023-03-28 13:45:00', '2023-03-25 10:00:00', 2, 0, '按时归还'),
(2, 6, '2023-04-01 15:30:00', '2023-05-01 15:30:00', NULL, 1, 1, '已续借一次'),
(3, 7, '2023-03-15 11:00:00', '2023-04-15 11:00:00', NULL, 1, 0, NULL),
(4, 8, '2023-04-05 14:20:00', '2023-05-05 14:20:00', NULL, 1, 0, NULL); 