-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS library_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE library_management;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `username` VARCHAR(50) NOT NULL,
  `password` VARCHAR(100) NOT NULL,
  `real_name` VARCHAR(50) NOT NULL,
  `email` VARCHAR(100),
  `phone` VARCHAR(20),
  `role` TINYINT DEFAULT 1 COMMENT '1-普通用户，2-管理员',
  `status` TINYINT DEFAULT 1 COMMENT '0-禁用, 1-正常',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_username` (`username`),
  UNIQUE INDEX `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 图书分类表
CREATE TABLE IF NOT EXISTS `category` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `category_name` VARCHAR(50) NOT NULL,
  `parent_id` BIGINT,
  `description` VARCHAR(200),
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`parent_id`) REFERENCES `category` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书分类表';

-- 图书表
CREATE TABLE IF NOT EXISTS `book` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `isbn` VARCHAR(20) NOT NULL,
  `title` VARCHAR(100) NOT NULL,
  `author` VARCHAR(100) NOT NULL,
  `publisher` VARCHAR(100) NOT NULL,
  `publish_date` DATE,
  `price` DECIMAL(10,2),
  `description` TEXT,
  `cover_url` VARCHAR(200),
  `category_id` BIGINT,
  `total_copies` INT DEFAULT 0,
  `available_copies` INT DEFAULT 0,
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_isbn` (`isbn`),
  FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE SET NULL,
  INDEX `idx_title` (`title`),
  INDEX `idx_author` (`author`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书表';

-- 借阅记录表
CREATE TABLE IF NOT EXISTS `borrow_record` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `book_id` BIGINT NOT NULL,
  `borrow_date` DATETIME NOT NULL,
  `due_date` DATETIME NOT NULL,
  `return_date` DATETIME,
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '1-借出, 2-已还, 3-逾期',
  `renew_count` INT DEFAULT 0,
  `remarks` VARCHAR(200),
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  FOREIGN KEY (`book_id`) REFERENCES `book` (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_book_id` (`book_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='借阅记录表';

-- 添加管理员账号
INSERT INTO `user` (`username`, `password`, `real_name`, `email`, `role`) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '系统管理员', '<EMAIL>', 2)
ON DUPLICATE KEY UPDATE `username` = `username`;

-- 添加默认图书分类
INSERT INTO `category` (`category_name`, `description`) VALUES 
('文学', '文学作品，包括小说、诗歌等'),
('科技', '科学技术相关书籍'),
('历史', '历史类书籍'),
('艺术', '艺术类书籍'),
('教育', '教育类书籍'),
('经济', '经济类书籍'),
('哲学', '哲学类书籍'),
('政治', '政治类书籍'),
('计算机', '计算机科学书籍')
ON DUPLICATE KEY UPDATE `category_name` = `category_name`; 