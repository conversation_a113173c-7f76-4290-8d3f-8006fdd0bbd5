# æå¡å¨éç½®
server.port=8080
server.servlet.context-path=/api

# Springéç½®
spring.main.allow-circular-references=true

# æ°æ®åºéç½®
spring.datasource.url=*****************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Admin@123
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# æ°æ®åå§å
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:database/schema.sql
spring.sql.init.data-locations=classpath:database/data.sql
spring.sql.init.continue-on-error=true

# HikariCPè¿æ¥æ± éç½®
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=12
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000

# jOOQéç½®
spring.jooq.sql-dialect=MYSQL

# Sessionéç½®
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.session.timeout=30m

# æ¥å¿éç½®
logging.level.root=INFO
logging.level.com.library.management=DEBUG
logging.level.org.jooq=DEBUG
logging.level.org.springframework.jdbc=DEBUG

# è·¨åéç½®
spring.mvc.cors.allowed-origins=http://localhost:8080,http://localhost,http://127.0.0.1:8080,http://127.0.0.1
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.mvc.cors.allowed-headers=Authorization,Content-Type,X-Requested-With,Accept,Origin
spring.mvc.cors.allow-credentials=true
spring.mvc.cors.max-age=3600 