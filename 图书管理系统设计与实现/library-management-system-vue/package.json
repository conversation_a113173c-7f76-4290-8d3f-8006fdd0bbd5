{"name": "library-management-system-vue", "version": "1.0.0", "description": "Library Management System - Vue Frontend", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "keywords": ["library", "management", "system", "vue"], "author": "", "license": "ISC", "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^1.5.0", "bootstrap": "^5.3.1", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.0", "echarts": "^5.6.0", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/node": "^20.17.30", "@vitejs/plugin-vue": "^4.3.4", "typescript": "^5.2.2", "vite": "^4.4.9", "vue-tsc": "^1.8.10"}}