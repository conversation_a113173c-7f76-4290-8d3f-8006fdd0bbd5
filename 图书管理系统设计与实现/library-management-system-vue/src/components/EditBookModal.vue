<template>
  <a-modal
    :visible="visible"
    title="编辑图书"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
  >
    <a-form
      :model="formState"
      :rules="rules"
      ref="formRef"
      layout="vertical"
    >
      <a-form-item label="书名" name="title">
        <a-input v-model:value="formState.title" />
      </a-form-item>
      <a-form-item label="作者" name="author">
        <a-input v-model:value="formState.author" />
      </a-form-item>
      <a-form-item label="ISBN" name="isbn">
        <a-input v-model:value="formState.isbn" />
      </a-form-item>
      <a-form-item label="出版社" name="publisher">
        <a-input v-model:value="formState.publisher" />
      </a-form-item>
      <a-form-item label="出版日期" name="publishDate">
        <a-date-picker
          v-model:value="formState.publishDate"
          style="width: 100%"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>
      <a-form-item label="分类" name="category">
        <a-select v-model:value="formState.category">
          <a-select-option value="计算机科学">计算机科学</a-select-option>
          <a-select-option value="文学艺术">文学艺术</a-select-option>
          <a-select-option value="自然科学">自然科学</a-select-option>
          <a-select-option value="社会科学">社会科学</a-select-option>
          <a-select-option value="其他">其他</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="总数量" name="totalCopies">
        <a-input-number v-model:value="formState.totalCopies" :min="0" style="width: 100%" />
      </a-form-item>
      <a-form-item label="可借数量" name="availableCopies">
        <a-input-number v-model:value="formState.availableCopies" :min="0" :max="formState.totalCopies" style="width: 100%" />
      </a-form-item>
      <a-form-item label="封面URL" name="coverUrl">
        <a-input v-model:value="formState.coverUrl" />
      </a-form-item>
      <a-form-item label="描述" name="description">
        <a-textarea v-model:value="formState.description" :rows="4" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { bookService } from '@/services/api';
import dayjs from 'dayjs';

const props = defineProps<{
  visible: boolean;
  book: any;
}>();

const emit = defineEmits(['update:visible', 'success']);

const formRef = ref<FormInstance>();
const confirmLoading = ref(false);

const formState = reactive({
  title: '',
  author: '',
  isbn: '',
  publisher: '',
  publishDate: '',
  category: '',
  description: '',
  coverUrl: '',
  totalCopies: 0,
  availableCopies: 0
});

const rules = {
  title: [{ required: true, message: '请输入书名' }],
  author: [{ required: true, message: '请输入作者' }],
  isbn: [{ required: true, message: '请输入ISBN' }],
  publisher: [{ required: true, message: '请输入出版社' }],
  publishDate: [{ required: true, message: '请选择出版日期' }],
  category: [{ required: true, message: '请选择分类' }],
  totalCopies: [{ required: true, message: '请输入总数量' }],
  availableCopies: [{ required: true, message: '请输入可借数量' }]
};

watch(() => props.book, (newBook) => {
  if (newBook) {
    Object.assign(formState, {
      ...newBook,
      publishDate: dayjs(newBook.publishDate)
    });
  }
}, { immediate: true });

const handleOk = async () => {
  try {
    await formRef.value?.validate();
    confirmLoading.value = true;
    
    const response = await bookService.updateBook(props.book.id, {
      ...formState,
      publishDate: formState.publishDate.format('YYYY-MM-DD')
    });

    if (response.data.success) {
      message.success('更新成功');
      emit('success');
      emit('update:visible', false);
    } else {
      message.error(response.data.message || '更新失败');
    }
  } catch (error: any) {
    if (error.errorFields) {
      message.error('请填写必填项');
    } else {
      message.error(error.response?.data?.message || '更新失败');
    }
  } finally {
    confirmLoading.value = false;
  }
};

const handleCancel = () => {
  emit('update:visible', false);
};
</script> 