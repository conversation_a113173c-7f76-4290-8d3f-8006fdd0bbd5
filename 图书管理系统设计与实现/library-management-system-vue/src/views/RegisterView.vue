<template>
  <div class="register-container">
    <div class="card register-card">
      <div class="card-header text-center">
        <h4 class="my-2">图书管理系统</h4>
      </div>
      <div class="card-body">
        <h5 class="card-title">用户注册</h5>
        <form @submit.prevent="handleRegister">
          <div class="mb-3">
            <label for="username" class="form-label">用户名</label>
            <input 
              type="text" 
              class="form-control" 
              id="username" 
              v-model="form.username" 
              required
            >
          </div>
          <div class="mb-3">
            <label for="realName" class="form-label">姓名</label>
            <input 
              type="text" 
              class="form-control" 
              id="realName" 
              v-model="form.realName" 
              required
            >
          </div>
          <div class="mb-3">
            <label for="email" class="form-label">邮箱</label>
            <input 
              type="email" 
              class="form-control" 
              id="email" 
              v-model="form.email" 
              required
            >
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">密码</label>
            <input 
              type="password" 
              class="form-control" 
              id="password" 
              v-model="form.password" 
              required
            >
          </div>
          <div class="mb-3">
            <label for="confirmPassword" class="form-label">确认密码</label>
            <input 
              type="password" 
              class="form-control" 
              id="confirmPassword" 
              v-model="form.confirmPassword" 
              required
            >
          </div>
          <div v-if="errorMessage" class="alert alert-danger">
            {{ errorMessage }}
          </div>
          <button 
            type="submit" 
            class="btn btn-primary w-100" 
            :disabled="loading"
          >
            {{ loading ? '注册中...' : '注册' }}
          </button>
          <div class="mt-3 text-center">
            <p>
              已有账号? 
              <router-link to="/login">登录</router-link>
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { authService } from '@/services/api';

const form = reactive({
  username: '',
  realName: '',
  email: '',
  password: '',
  confirmPassword: ''
});

const errorMessage = ref('');
const loading = ref(false);
const router = useRouter();

const handleRegister = async () => {
  // 验证密码
  if (form.password !== form.confirmPassword) {
    errorMessage.value = '两次输入的密码不一致';
    return;
  }
  
  loading.value = true;
  errorMessage.value = '';
  
  try {
    const userData = {
      username: form.username,
      password: form.password,
      realName: form.realName,
      email: form.email,
      role: 1, // 普通用户
      status: 1 // 启用状态
    };
    
    await authService.register(userData);
    router.push('/login');
  } catch (error: any) {
    console.error('注册失败', error);
    errorMessage.value = error.response?.data?.message || '注册失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.register-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style> 