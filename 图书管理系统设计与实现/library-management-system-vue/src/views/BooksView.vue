<template>
  <div class="books-container">
    <h1>图书管理</h1>
    <div class="d-flex justify-content-between mb-4">
      <div class="d-flex gap-2">
        <input
          type="text"
          class="form-control"
          placeholder="搜索图书..."
          v-model="searchQuery"
          @input="handleSearch"
        />
        <button class="btn btn-outline-secondary" @click="handleSearch">
          搜索
        </button>
      </div>
      <button class="btn btn-primary" @click="openAddModal">
        添加图书
      </button>
    </div>

    <div class="card">
      <div class="card-body">
        <div v-if="loading" class="text-center my-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
        </div>

        <div v-else-if="error" class="alert alert-danger" role="alert">
          {{ error }}
        </div>
        
        <div v-else-if="books.length === 0" class="text-center my-5">
          <p class="text-muted">暂无图书数据</p>
        </div>
        
        <div v-else class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
          <div 
            v-for="book in books" 
            :key="book.id" 
            class="col"
          >
            <div class="card h-100">
              <div class="book-cover-container">
                <img
                  :src="book.coverUrl || 'https://via.placeholder.com/150x200'"
                  alt="Book cover"
                  class="card-img-top book-cover"
                />
              </div>
              <div class="card-body">
                <h5 class="card-title">{{ book.title }}</h5>
                <p class="card-text text-muted">{{ book.author }}</p>
                <p class="card-text">
                  <small class="text-muted">
                    <span class="badge bg-secondary me-2">{{ book.category }}</span>
                    库存: {{ book.availableCopies }}/{{ book.totalCopies }}
                  </small>
                </p>
                <p class="card-text">
                  <small class="text-muted">
                    ISBN: {{ book.isbn }}<br>
                    出版社: {{ book.publisher }}<br>
                    出版日期: {{ book.publishDate }}
                  </small>
                </p>
              </div>
              <div class="card-footer">
                <div class="d-flex justify-content-between">
                  <router-link 
                    :to="`/books/${book.id}`" 
                    class="btn btn-sm btn-outline-primary"
                  >
                    详情
                  </router-link>
                  <div>
                    <button 
                      class="btn btn-sm btn-outline-secondary me-2"
                      @click="editBook(book)"
                    >
                      编辑
                    </button>
                    <button 
                      class="btn btn-sm btn-outline-danger"
                      @click="deleteBook(book)"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑图书模态框 -->
    <edit-book-modal
      v-model:visible="editModalVisible"
      :book="selectedBook"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { bookService } from '@/services/api';
import EditBookModal from '@/components/EditBookModal.vue';

interface Book {
  id: number;
  title: string;
  author: string;
  isbn: string;
  publisher: string;
  publishDate: string;
  category: string;
  description: string;
  coverUrl: string;
  totalCopies: number;
  availableCopies: number;
  status: number;
  createdTime: string;
  updatedTime: string;
}

const books = ref<Book[]>([]);
const loading = ref(false);
const searchQuery = ref('');
const error = ref('');
const editModalVisible = ref(false);
const selectedBook = ref<Book | null>(null);

const fetchBooks = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    const response = await bookService.getAllBooks();
    if (response.data.success) {
      books.value = response.data.data;
    } else {
      error.value = response.data.message || '获取图书列表失败';
    }
  } catch (e: any) {
    console.error('获取图书列表失败:', e);
    error.value = e.response?.data?.message || '获取图书列表失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  fetchBooks(); // 暂时简单刷新列表，后续可以添加搜索功能
};

const openAddModal = () => {
  // TODO: 实现添加图书功能
  console.log('打开添加图书模态框');
};

const editBook = (book: Book) => {
  selectedBook.value = book;
  editModalVisible.value = true;
};

const handleEditSuccess = () => {
  fetchBooks(); // 刷新图书列表
};

const deleteBook = async (book: Book) => {
  if (!confirm(`确定要删除图书《${book.title}》吗？`)) {
    return;
  }

  try {
    const response = await bookService.deleteBook(book.id);
    if (response.data.success) {
      alert('删除成功！');
      await fetchBooks(); // 刷新列表
    }
  } catch (e: any) {
    console.error('删除图书失败:', e);
    alert(e.response?.data?.message || '删除图书失败，请稍后再试');
  }
};

onMounted(() => {
  fetchBooks();
});
</script>

<style scoped>
.books-container {
  padding: 20px;
}

.book-cover-container {
  height: 200px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.book-cover {
  max-height: 100%;
  object-fit: contain;
}
</style> 