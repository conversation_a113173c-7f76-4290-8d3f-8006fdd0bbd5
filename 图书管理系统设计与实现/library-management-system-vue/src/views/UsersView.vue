<template>
  <div class="container mt-4">
    <h2>用户管理</h2>
    <div class="card">
      <div class="card-body">
        <div class="mb-3 d-flex justify-content-end">
          <button class="btn btn-primary" @click="showCreateModal = true">
            新增用户
          </button>
        </div>
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>角色</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in users" :key="user.id">
                <td>{{ user.id }}</td>
                <td>{{ user.username }}</td>
                <td>{{ user.role === 1 ? '管理员' : '普通用户' }}</td>
                <td>
                  <span :class="user.status === 1 ? 'text-success' : 'text-danger'">
                    {{ user.status === 1 ? '正常' : '禁用' }}
                  </span>
                </td>
                <td>
                  <button class="btn btn-sm btn-primary me-2" @click="handleEdit(user)">
                    编辑
                  </button>
                  <button 
                    class="btn btn-sm" 
                    :class="user.status === 1 ? 'btn-danger' : 'btn-success'"
                    @click="handleToggleStatus(user)"
                  >
                    {{ user.status === 1 ? '禁用' : '启用' }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { userService } from '@/services/api';
import type { User } from '@/types';

const users = ref<User[]>([]);
const showCreateModal = ref(false);

const fetchUsers = async () => {
  try {
    const response = await userService.getAllUsers();
    users.value = response.data.data;
  } catch (error) {
    message.error('获取用户列表失败');
  }
};

const handleEdit = (user: User) => {
  // TODO: 实现编辑用户功能
  console.log('编辑用户:', user);
};

const handleToggleStatus = async (user: User) => {
  try {
    const newStatus = user.status === 1 ? 2 : 1;
    await userService.updateStatus(user.id, newStatus);
    message.success(`${newStatus === 1 ? '启用' : '禁用'}用户成功`);
    await fetchUsers();
  } catch (error) {
    message.error(`${user.status === 1 ? '禁用' : '启用'}用户失败`);
  }
};

onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.table th, .table td {
  vertical-align: middle;
}
</style> 