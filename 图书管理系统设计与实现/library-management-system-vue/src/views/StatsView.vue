<template>
  <div class="stats-container">
    <a-row :gutter="[16, 16]">
      <a-col :span="24">
        <a-card title="图书分类统计">
          <template #extra>
            <a-space>
              <a-select v-model:value="borrowPeriod" style="width: 120px">
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="month">本月</a-select-option>
                <a-select-option value="year">本年</a-select-option>
              </a-select>
              <a-button type="primary" @click="exportReport">导出报告</a-button>
            </a-space>
          </template>
          <div ref="chartRef" style="width: 100%; height: 400px"></div>
        </a-card>
      </a-col>
      <a-col :span="24">
        <a-card title="热门图书">
          <template #extra>
            <a-select v-model:value="popularTimeRange" style="width: 120px">
              <a-select-option value="week">本周</a-select-option>
              <a-select-option value="month">本月</a-select-option>
              <a-select-option value="year">本年</a-select-option>
            </a-select>
          </template>
          <a-table
            :columns="columns"
            :data-source="popularBooks"
            :loading="loading"
            :pagination="false"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'rank'">
                {{ record.rank }}
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import { statsService } from '@/services/api'

const chartRef = ref<HTMLElement>()
const chart = ref<echarts.ECharts>()
const borrowPeriod = ref('all')
const popularTimeRange = ref('week')
const loading = ref(false)
const popularBooks = ref<any[]>([])

const columns = [
  {
    title: '排名',
    dataIndex: 'rank',
    key: 'rank',
    width: 80,
    align: 'center'
  },
  {
    title: '书名',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true
  },
  {
    title: '借阅次数',
    dataIndex: 'borrowCount',
    key: 'borrowCount',
    width: 120,
    align: 'center'
  }
]

const initChart = () => {
  if (chartRef.value) {
    chart.value = echarts.init(chartRef.value)
    window.addEventListener('resize', handleResize)
  }
}

const handleResize = () => {
  chart.value?.resize()
}

const fetchBorrowStats = async () => {
  try {
    const response = await statsService.getBorrowStats(borrowPeriod.value)
    console.log('借阅统计数据:', response.data)
    if (!response.data || !response.data.data) {
      console.warn('借阅统计数据格式不正确:', response.data)
      updateChart({ categoryStats: [] })
      return
    }
    updateChart(response.data.data)
  } catch (error) {
    console.error('获取借阅统计数据失败:', error)
    message.error('获取借阅统计数据失败')
    updateChart({ categoryStats: [] })
  }
}

const updateChart = (data: any) => {
  if (!chart.value) return;

  // 处理分类统计数据
  const categoryNames = {
    1: '计算机科学',
    2: '文学艺术',
    3: '自然科学',
    4: '社会科学',
    5: '其他'
  };

  const categories = data.categoryStats || [];
  const chartData = categories.map((item: any) => ({
    name: categoryNames[item.categoryId as keyof typeof categoryNames] || `分类${item.categoryId}`,
    value: item.count
  }));

  const option: EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} 本 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [
      {
        name: '借阅统计',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} 本'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: chartData
      }
    ]
  };

  chart.value.setOption(option);
}

const fetchPopularBooks = async () => {
  loading.value = true
  try {
    const response = await statsService.getPopularBooks(popularTimeRange.value)
    console.log('热门图书原始数据:', response.data)
    
    if (!response.data || !response.data.data) {
      console.warn('热门图书数据格式不正确:', response.data)
      popularBooks.value = []
      return
    }

    popularBooks.value = response.data.data.map((book: any, index: number) => ({
      ...book,
      rank: index + 1,
      key: book.id || index
    }))
    
    console.log('处理后的热门图书数据:', popularBooks.value)
  } catch (error) {
    console.error('获取热门图书数据失败:', error)
    message.error('获取热门图书数据失败')
    popularBooks.value = []
  } finally {
    loading.value = false
  }
}

const exportReport = async () => {
  try {
    await statsService.exportReport(borrowPeriod.value)
    message.success('报告导出成功')
  } catch (error) {
    message.error('报告导出失败')
  }
}

watch(borrowPeriod, () => {
  fetchBorrowStats()
})

watch(popularTimeRange, () => {
  fetchPopularBooks()
})

onMounted(() => {
  initChart()
  fetchBorrowStats()
  fetchPopularBooks()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chart.value?.dispose()
})
</script>

<style scoped>
.stats-container {
  padding: 24px;
}
</style> 