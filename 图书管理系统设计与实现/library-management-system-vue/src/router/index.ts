import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/',
            name: 'home',
            component: () => import('@/views/HomeView.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/views/LoginView.vue'),
            meta: { requiresAuth: false }
        },
        {
            path: '/register',
            name: 'register',
            component: () => import('@/views/RegisterView.vue'),
            meta: { requiresAuth: false }
        },
        {
            path: '/books',
            name: 'books',
            component: () => import('@/views/BooksView.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/books/:id',
            name: 'book-detail',
            component: () => import('@/views/BookDetailView.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/borrows',
            name: 'borrows',
            component: () => import('@/views/BorrowsView.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/users',
            name: 'users',
            component: () => import('@/views/UsersView.vue'),
            meta: { requiresAuth: true, requiresAdmin: true }
        },
        {
            path: '/stats',
            name: 'stats',
            component: () => import('@/views/StatsView.vue'),
            meta: {
                requiresAuth: true,
                title: '统计分析'
            }
        }
    ]
});

router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore();
    
    // 检查是否需要认证
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
        next({ name: 'login', query: { redirect: to.fullPath } });
        return;
    }
    
    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
        next({ name: 'home' });
        return;
    }
    
    // 如果已登录且访问登录/注册页，重定向到首页
    if (authStore.isAuthenticated && (to.name === 'login' || to.name === 'register')) {
        next({ name: 'home' });
        return;
    }
    
    next();
});

export default router; 