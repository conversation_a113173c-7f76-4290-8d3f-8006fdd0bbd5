<template>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <router-link class="navbar-brand" to="/">
                <i class="bi bi-book me-2"></i>图书管理系统
            </router-link>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <router-link class="nav-link" to="/" active-class="active">
                            <i class="bi bi-house-door me-1"></i>首页
                        </router-link>
                    </li>
                    <li class="nav-item">
                        <router-link class="nav-link" to="/books" active-class="active">
                            <i class="bi bi-book-half me-1"></i>图书管理
                        </router-link>
                    </li>
                    <li class="nav-item">
                        <router-link class="nav-link" to="/borrows" active-class="active">
                            <i class="bi bi-arrow-left-right me-1"></i>借阅管理
                        </router-link>
                    </li>
                    <li class="nav-item">
                        <router-link class="nav-link" to="/stats" active-class="active">
                            <i class="bi bi-bar-chart me-1"></i>统计分析
                        </router-link>
                    </li>
                    <li class="nav-item" v-if="authStore.isAdmin">
                        <router-link class="nav-link" to="/users" active-class="active">
                            <i class="bi bi-people me-1"></i>用户管理
                        </router-link>
                    </li>
                </ul>
                <div class="d-flex" v-if="!authStore.isAuthenticated">
                    <router-link class="btn btn-outline-light me-2" to="/login">
                        <i class="bi bi-box-arrow-in-right me-1"></i>登录
                    </router-link>
                    <router-link class="btn btn-outline-light" to="/register">
                        <i class="bi bi-person-plus me-1"></i>注册
                    </router-link>
                </div>
                <div class="d-flex align-items-center" v-else>
                    <span class="text-light me-3">
                        <i class="bi bi-person-circle me-1"></i>{{ authStore.user?.realName }}
                    </span>
                    <button class="btn btn-outline-light" @click="handleLogout">
                        <i class="bi bi-box-arrow-right me-1"></i>退出
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <router-view></router-view>
    </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const authStore = useAuthStore();
const router = useRouter();

onMounted(async () => {
    await authStore.checkAuth();
});

const handleLogout = async () => {
    await authStore.logout();
    router.push('/login');
};
</script>

<style>
@import 'bootstrap/dist/css/bootstrap.min.css';
@import 'bootstrap-icons/font/bootstrap-icons.css';
</style> 