export interface User {
    id?: number;
    username: string;
    password?: string;
    realName: string;
    email: string;
    role: number;
    status: number;
    token?: string;
}

export interface Book {
    id?: number;
    title: string;
    author: string;
    isbn: string;
    publisher: string;
    publishDate: string;
    price: number;
    stock: number;
    coverUrl?: string;
    description?: string;
}

export interface Borrow {
    id?: number;
    userId: number;
    bookId: number;
    borrowDate: string;
    dueDate: string;
    returnDate?: string;
    status: number;
    renewCount: number;
}

export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    message?: string;
    code?: number;
}

export interface LoginForm {
    username: string;
    password: string;
}

export interface RegisterForm extends LoginForm {
    realName: string;
    email: string;
    confirmPassword: string;
}

export interface Review {
    id: number;
    bookId: number;
    userId: number;
    rating: number;
    comment: string;
    createdAt: string;
    updatedAt: string;
    user?: User;
} 