import { defineStore } from 'pinia';
import type { User } from '@/types';
import { authService } from '@/services/api';

export const useAuthStore = defineStore('auth', {
    state: () => ({
        user: null as User | null,
        token: localStorage.getItem('token') || null,
        loading: false,
        error: null as string | null
    }),

    getters: {
        isAuthenticated: (state) => !!state.token,
        isAdmin: (state) => state.user?.role === 2
    },

    actions: {
        async login(username: string, password: string) {
            this.loading = true;
            this.error = null;
            try {
                const response = await authService.login(username, password);
                if (response.data.success && response.data.data) {
                    this.user = response.data.data;
                    const token = response.data.data.token || 'dummy-token';
                    this.token = token;
                    localStorage.setItem('token', token);
                    localStorage.setItem('user', JSON.stringify(this.user));
                }
            } catch (error: any) {
                this.error = error.response?.data?.message || '登录失败';
                throw error;
            } finally {
                this.loading = false;
            }
        },

        async register(userData: User) {
            this.loading = true;
            this.error = null;
            try {
                const response = await authService.register(userData);
                if (response.data.success && response.data.data) {
                    this.user = response.data.data;
                    const token = response.data.data.token || 'dummy-token';
                    this.token = token;
                    localStorage.setItem('token', token);
                    localStorage.setItem('user', JSON.stringify(this.user));
                }
            } catch (error: any) {
                this.error = error.response?.data?.message || '注册失败';
                throw error;
            } finally {
                this.loading = false;
            }
        },

        async logout() {
            try {
                await authService.logout();
            } finally {
                this.user = null;
                this.token = null;
                localStorage.removeItem('token');
                localStorage.removeItem('user');
            }
        },

        async checkAuth() {
            if (this.token) {
                try {
                    const response = await authService.getCurrentUser();
                    if (response.data.success && response.data.data) {
                        this.user = response.data.data;
                    } else {
                        this.logout();
                    }
                } catch (error) {
                    this.logout();
                }
            }
        }
    }
}); 