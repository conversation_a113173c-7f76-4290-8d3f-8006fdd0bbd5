# 图书管理系统

一个基于 Spring Boot 和 Vue 3 的图书管理系统。

## 项目结构

```
bs/
├── library-management-system/       # 后端项目（Spring Boot）
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── com/library/management/
│   │   │   │       ├── config/          # 配置类
│   │   │   │       ├── controller/      # 控制器
│   │   │   │       ├── service/         # 服务层
│   │   │   │       ├── repository/      # 数据访问层
│   │   │   │       ├── model/           # 数据模型
│   │   │   │       │   ├── entity/      # 实体类
│   │   │   │       │   ├── dto/         # 数据传输对象
│   │   │   │       │   └── common/      # 通用模型
│   │   │   │       └── util/            # 工具类
│   │   │   └── resources/
│   │   │       ├── application.properties
│   │   │       └── database/            # 数据库脚本
│   │   └── test/                        # 测试代码
│   ├── pom.xml
│   └── README.md
│
├── library-management-system-vue/   # 前端项目（Vue 3）
│   ├── src/
│   │   ├── assets/                  # 静态资源
│   │   ├── components/              # 公共组件
│   │   ├── views/                   # 页面视图
│   │   ├── router/                  # 路由配置
│   │   ├── services/                # API 服务
│   │   ├── types/                   # TypeScript 类型定义
│   │   ├── config/                  # 配置
│   │   ├── App.vue                  # 根组件
│   │   └── main.ts                  # 入口文件
│   ├── public/                      # 公共资源
│   ├── package.json
│   ├── tsconfig.json
│   └── vite.config.ts
│
├── .gitignore                       # Git 忽略文件
├── package.json                     # 根项目依赖
├── package-lock.json                # 依赖锁定文件
└── README.md                        # 项目说明文档
```

## 技术栈

### 后端
- Spring Boot 2.7.0
- Spring Security
- jOOQ
- MySQL 8.0
- Maven

### 前端
- Vue 3
- TypeScript
- Vite
- Pinia
- Vue Router
- Bootstrap 5
- Axios

## 开发环境要求

- JDK 11+
- Node.js 16+
- MySQL 8.0+

## 快速开始

### 后端

1. 创建数据库：
```sql
CREATE DATABASE library_management;
```

2. 配置数据库连接：
编辑 `library-management-system/src/main/resources/application.properties` 文件，修改数据库连接信息。

3. 启动后端服务：
```bash
cd library-management-system
mvn spring-boot:run
```

### 前端

1. 安装依赖：
```bash
cd library-management-system-vue
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

## 功能特性

- 用户管理（注册、登录、权限控制）
- 图书管理（增删改查）
- 借阅管理（借书、还书、续借）
- 统计分析（借阅统计、图书统计）

## 版本控制注意事项

项目已配置 `.gitignore` 文件，以下内容不会被纳入版本控制：
- `node_modules/` 依赖包目录
- 构建输出目录 (`dist/`, `build/`)
- IDE 配置文件 (`.vscode/`, `.idea/`)
- 日志文件和操作系统临时文件

克隆项目后需要运行 `npm install` 安装依赖。

## 许可证

MIT 