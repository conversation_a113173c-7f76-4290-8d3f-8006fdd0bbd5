import os
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL

# 创建一个新的Document对象
doc = Document()

# 设置页面边距
sections = doc.sections
for section in sections:
    section.top_margin = Inches(1)
    section.bottom_margin = Inches(1)
    section.left_margin = Inches(1.25)
    section.right_margin = Inches(1.25)

# 添加标题
title = doc.add_paragraph("本科毕业论文（设计）开题报告")
title.alignment = WD_ALIGN_PARAGRAPH.CENTER
title_run = title.runs[0]
title_run.font.name = '方正小标宋简体'
title_run.font.size = Pt(20)
title_run.font.bold = True

# 添加基本信息表格
info_table = doc.add_table(rows=6, cols=2)
info_table.style = 'Table Grid'
info_table.alignment = WD_ALIGN_PARAGRAPH.CENTER

# 设置表格各列宽度
info_table.columns[0].width = Inches(1.5)
info_table.columns[1].width = Inches(3)

# 填充表格内容
info_data = [
    ("姓    名:", ""),
    ("学    号:", ""),
    ("学    院:", ""),
    ("专    业:", ""),
    ("班    级:", ""),
    ("指导教师:", "")
]

for i, (label, value) in enumerate(info_data):
    cell = info_table.cell(i, 0)
    cell.text = label
    cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    
    cell = info_table.cell(i, 1)
    cell.text = value
    cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER

# 添加空行
doc.add_paragraph()

# 添加毕业论文题目
topic_table = doc.add_table(rows=1, cols=2)
topic_table.style = 'Table Grid'

topic_table.columns[0].width = Inches(1.5)
topic_table.columns[1].width = Inches(4)

cell = topic_table.cell(0, 0)
cell.text = "毕业论文（设计）题目"
cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER

cell = topic_table.cell(0, 1)
cell.text = "图书管理系统设计与实现"
cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER

# 添加选题背景及意义
doc.add_paragraph()
bg_title = doc.add_paragraph("一、选题背景及意义")
bg_title.style = 'Heading 1'
bg_title.alignment = WD_ALIGN_PARAGRAPH.LEFT

bg_content = doc.add_paragraph(
"""图书馆作为知识的宝库，一直是人类智慧传承的重要场所。随着社会信息化的快速发展，传统的手工管理方式已难以满足现代图书馆管理的需求。图书数量的爆炸性增长、读者需求的多样化以及资源共享的迫切需要，都对图书馆的管理提出了更高的要求。在这样的背景下，开发一套功能完善、操作简便、安全可靠的图书管理系统具有十分重要的现实意义。

传统的图书管理模式主要依靠人工操作，不仅工作强度大、效率低，而且容易出现数据不一致、信息丢失等问题。随着计算机技术和互联网技术的发展，图书管理系统已经由最初的简单的目录管理系统发展为集成了多种功能的综合性信息管理平台。现代图书管理系统不仅能够实现基本的图书借阅管理功能，还能够提供数据分析、智能推荐、资源共享等高级服务。

本课题旨在设计和实现一个功能完备、界面友好、性能稳定的图书管理系统，可以帮助图书馆工作人员高效地管理图书资源，为读者提供便捷的图书查阅和借阅服务。该系统的实现将大大提高图书馆的管理效率，降低管理成本，优化资源配置，提升用户体验，为构建现代化、智能化的图书馆信息服务体系奠定基础。"""
)
bg_content.alignment = WD_ALIGN_PARAGRAPH.LEFT
for run in bg_content.runs:
    run.font.size = Pt(12)

# 添加文献综述
doc.add_paragraph()
lit_title = doc.add_paragraph("二、文献综述")
lit_title.style = 'Heading 1'
lit_title.alignment = WD_ALIGN_PARAGRAPH.LEFT

lit_content = doc.add_paragraph(
"""图书管理系统的研究与应用已有较长历史，从最早的卡片式管理，到电子文档管理，再到如今的智能化网络管理系统，经历了多个发展阶段。在国内外，图书管理系统的研究主要集中在以下几个方面：

传统图书管理系统的发展：20世纪60年代，美国首先开发了图书管理自动化系统MARC（Machine Readable Cataloging），实现了图书目录的计算机化管理。80年代，随着个人计算机的普及，单机版图书管理软件开始出现。Chen等人(2002)研究表明，早期的图书管理系统主要关注于图书编目和流通管理，功能相对单一。

网络化图书管理系统：1990年代，随着互联网的兴起，基于Web的图书管理系统逐渐成为主流。Smith和Johnson(2005)提出了基于B/S架构的图书管理系统设计方案，实现了图书馆资源的远程访问和管理。Wang等(2010)进一步研究了分布式图书管理系统的设计与实现，解决了多分馆图书资源共享的问题。

数据库技术在图书管理中的应用：数据库技术是图书管理系统的核心技术之一。Zhang(2012)比较了关系型数据库和NoSQL数据库在图书管理系统中的应用，指出针对不同的业务需求，选择合适的数据库类型至关重要。Liu等(2015)研究了大数据技术在图书管理中的应用，提出了基于Hadoop的大规模图书数据处理方案。

移动互联网时代的图书管理：随着智能手机的普及，基于移动端的图书管理应用也逐渐增多。Wilson等(2018)研究了移动图书馆应用的用户体验设计，强调了简洁界面和便捷操作的重要性。国内学者李明(2019)提出了基于微信小程序的图书馆服务平台设计，实现了读者在移动终端上完成图书检索、预约、续借等操作。

智能化图书管理系统：近年来，人工智能技术在图书管理中的应用成为研究热点。Brown等(2020)探讨了机器学习算法在图书推荐系统中的应用，通过分析读者借阅历史和偏好，为用户推荐可能感兴趣的图书。张华等(2021)研究了基于RFID技术的智能图书管理系统，实现了图书的自动识别和定位，大大提高了图书盘点和查找的效率。

综上所述，图书管理系统的研究已经从单纯的信息管理发展到智能化服务，未来的发展趋势是将人工智能、大数据、云计算等先进技术进一步融入图书管理系统，实现更加智能化、个性化的图书馆服务。"""
)
lit_content.alignment = WD_ALIGN_PARAGRAPH.LEFT
for run in lit_content.runs:
    run.font.size = Pt(12)

# 添加毕业论文的主要内容
doc.add_paragraph()
content_title = doc.add_paragraph("三、毕业论文的主要内容、预期目标及拟解决的关键问题")
content_title.style = 'Heading 1'
content_title.alignment = WD_ALIGN_PARAGRAPH.LEFT

# 3.1 数据库设计与实现
sub_title = doc.add_paragraph("3.1 数据库设计与实现")
sub_title.style = 'Heading 2'
sub_content = doc.add_paragraph(
"""本系统将采用关系型数据库(MySQL)存储图书、用户、借阅等信息。数据库设计将遵循规范化原则，确保数据的一致性和完整性。主要数据表包括图书信息表、用户信息表、借阅记录表、图书分类表、管理员信息表等。将对各表之间的关系进行详细设计，并进行适当的索引优化，提高查询效率。"""
)

# 3.2 系统架构设计
sub_title = doc.add_paragraph("3.2 系统架构设计")
sub_title.style = 'Heading 2'
sub_content = doc.add_paragraph(
"""系统采用B/S(Browser/Server)架构，基于Spring Boot框架开发后端，前端使用Vue.js框架，实现前后端分离的开发模式。系统将划分为表示层、业务逻辑层、数据访问层三层结构，各层之间通过接口进行通信，降低系统耦合度，提高可维护性和可扩展性。"""
)

# 3.3 功能模块设计与实现
sub_title = doc.add_paragraph("3.3 功能模块设计与实现")
sub_title.style = 'Heading 2'
sub_content = doc.add_paragraph(
"""系统将包含以下主要功能模块：

1. 用户管理模块：实现用户注册、登录、信息修改等基本功能。系统将区分普通用户和管理员用户，不同类型用户具有不同的操作权限。

2. 图书信息管理模块：实现图书信息的添加、修改、删除、查询等功能。包括图书基本信息(书名、作者、出版社、ISBN等)的管理，以及图书库存状态的维护。

3. 图书借阅管理模块：实现图书借阅、归还、续借等基本功能。系统将记录借阅信息，计算归还日期，并提供基本的逾期提醒。

4. 图书检索模块：提供基本的图书检索功能，包括按书名、作者等条件进行简单查询，支持模糊查询。

5. 统计分析模块：提供基本的统计功能，如借阅量统计和热门图书排行，为图书馆管理提供数据参考。"""
)

# 3.4 系统安全设计
sub_title = doc.add_paragraph("3.4 系统安全设计")
sub_title.style = 'Heading 2'
sub_content = doc.add_paragraph(
"""系统将采取基本的安全措施，保障系统和数据安全：

1. 用户身份认证：采用基于Session的身份认证机制，确保用户安全登录。
2. 权限控制：简化为基本角色区分（如普通用户、管理员），每种角色有固定权限。
3. 数据安全：提供手动数据备份功能和恢复建议，防止数据丢失。
4. 日志记录：记录关键操作日志（如登录、借还书），便于故障追踪。
5. 基本输入验证：使用框架提供的基本防护功能，防止常见的安全问题。"""
)

# 3.5 系统测试与优化
sub_title = doc.add_paragraph("3.5 系统测试与优化")
sub_title.style = 'Heading 2'
sub_content = doc.add_paragraph(
"""系统开发完成后，将进行全面的测试，包括单元测试、集成测试、性能测试和用户接受度测试。根据测试结果，对系统进行优化，提高系统的稳定性、响应速度和用户体验。"""
)

# 3.6 开发环境
sub_title = doc.add_paragraph("3.6 开发环境")
sub_title.style = 'Heading 2'
sub_content = doc.add_paragraph(
"""- 开发语言：Java(后端)，JavaScript(前端)
- 前端框架：Vue.js（基础版）
- 后端框架：Spring Boot, Spring MVC
- 数据访问框架：jOOQ
- 数据库：MySQL
- 开发工具：IntelliJ IDEA, VS Code, Git
- 服务器环境：Tomcat"""
)

# 3.7 总体流程框架
sub_title = doc.add_paragraph("3.7 总体流程框架")
sub_title.style = 'Heading 2'
sub_content = doc.add_paragraph(
"""系统的总体工作流程包括：
1. 用户通过浏览器访问系统，进行身份认证
2. 根据用户类型和权限，显示相应的功能界面
3. 用户进行各种操作(如图书检索、借阅、归还等)
4. 系统接收请求，调用相应的业务逻辑处理
5. 业务逻辑层与数据库交互，完成数据操作
6. 将处理结果返回给用户
7. 系统记录操作日志，更新相关统计数据"""
)

# 添加毕业论文提纲
doc.add_paragraph()
outline_title = doc.add_paragraph("四、毕业论文提纲")
outline_title.style = 'Heading 1'
outline_title.alignment = WD_ALIGN_PARAGRAPH.LEFT

# 论文题目
doc.add_paragraph("论文题目：图书管理系统设计与实现", style='Heading 2')

# 论文大纲
doc.add_paragraph("论文大纲：", style='Heading 2')

# 第一章
doc.add_paragraph("第一章 绪论", style='Heading 3')
doc.add_paragraph("1.1 研究背景及意义")
doc.add_paragraph("1.2 国内外研究现状")
doc.add_paragraph("1.3 研究内容与方法")
doc.add_paragraph("1.4 论文组织结构")

# 第二章
doc.add_paragraph("第二章 系统需求分析", style='Heading 3')
doc.add_paragraph("2.1 业务需求分析")
doc.add_paragraph("2.2 功能需求分析")
doc.add_paragraph("2.3 非功能需求分析")
doc.add_paragraph("2.4 系统可行性分析")

# 第三章
doc.add_paragraph("第三章 系统总体设计", style='Heading 3')
doc.add_paragraph("3.1 设计原则与目标")
doc.add_paragraph("3.2 系统架构设计")
doc.add_paragraph("3.3 功能模块设计")
doc.add_paragraph("3.4 数据库设计")
doc.add_paragraph("3.5 系统安全设计")

# 第四章
doc.add_paragraph("第四章 系统详细设计与实现", style='Heading 3')
doc.add_paragraph("4.1 开发环境配置")
doc.add_paragraph("4.2 用户管理模块实现")
doc.add_paragraph("4.3 图书信息管理模块实现")
doc.add_paragraph("4.4 图书借阅管理模块实现")
doc.add_paragraph("4.5 图书检索模块实现")
doc.add_paragraph("4.6 统计分析模块实现")

# 第五章
doc.add_paragraph("第五章 系统测试", style='Heading 3')
doc.add_paragraph("5.1 测试环境及方法")
doc.add_paragraph("5.2 功能测试")
doc.add_paragraph("5.3 性能测试")
doc.add_paragraph("5.4 安全测试")
doc.add_paragraph("5.5 测试结果分析")

# 第六章
doc.add_paragraph("第六章 系统部署与维护", style='Heading 3')
doc.add_paragraph("6.1 系统部署方案")
doc.add_paragraph("6.2 系统维护策略")
doc.add_paragraph("6.3 用户培训方案")
doc.add_paragraph("6.4 系统扩展设计")

# 第七章
doc.add_paragraph("第七章 总结与展望", style='Heading 3')
doc.add_paragraph("7.1 研究工作总结")
doc.add_paragraph("7.2 系统创新点")
doc.add_paragraph("7.3 不足与改进方向")
doc.add_paragraph("7.4 未来研究展望")

# 参考文献和致谢
doc.add_paragraph("参考文献", style='Heading 3')
doc.add_paragraph("致谢", style='Heading 3')

# 添加毕业论文工作计划
doc.add_paragraph()
plan_title = doc.add_paragraph("五、毕业论文工作计划")
plan_title.style = 'Heading 1'
plan_title.alignment = WD_ALIGN_PARAGRAPH.LEFT

# 添加工作计划表格
plan_table = doc.add_table(rows=6, cols=3)
plan_table.style = 'Table Grid'

# 表头
headers = ["序号", "起止时间", "各阶段工作内容"]
for i, header in enumerate(headers):
    cell = plan_table.cell(0, i)
    cell.text = header
    cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER

# 表格内容
plan_data = [
    ("1", "2024.10.20-2024.11.22", "查阅文献，确定论文题目，完成开题报告，并进行开题答辩。"),
    ("2", "2024.11.23-2024.12.15", "完成系统需求分析和总体设计，包括系统功能模块划分、数据库设计、系统架构设计等。"),
    ("3", "2024.12.16-2025.2.15", "进行系统详细设计与实现，完成各功能模块的编码工作，实现系统的基本功能。"),
    ("4", "2025.02.16-2025.03.09", "进行系统测试与优化，修复bug，完善功能，提高系统性能和用户体验。"),
    ("5", "2025.03.10-2025.04.13", "撰写毕业论文、修改毕业论文、准备答辩。")
]

for i, (num, time, content) in enumerate(plan_data):
    row = plan_table.rows[i+1]
    cells = row.cells
    
    cells[0].text = num
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    cells[0].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    
    cells[1].text = time
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    cells[1].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    
    cells[2].text = content
    cells[2].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    cells[2].vertical_alignment = WD_ALIGN_VERTICAL.CENTER

# 添加论文类型与选题来源
doc.add_paragraph()
doc.add_paragraph("论文类型", style='Heading 2')
doc.add_paragraph("☐ 理论研究     ☐ 应用研究     ☑ 技术开发     ☐ 工程设计")

doc.add_paragraph()
doc.add_paragraph("选题来源", style='Heading 2')
doc.add_paragraph("""☐ 国家重点研发计划项目  ☐ 国家社科规划、基金项目
☐ 国家自然科学基金项目  ☐ 中央、国家各部门项目
☐ 教育部人文、社会科学研究项目  ☐ 省（自治区、直辖市）项目
☐ 国际合作研究项目      ☐ 与港、澳、台合作研究项目
☐ 企、事业单位委托项目  ☐ 外资项目
☐ 国防项目              ☑ 学校自选项目
☑ 非立项                ☐ 其他""")

# 添加指导教师意见
doc.add_paragraph()
opinion_title = doc.add_paragraph("指导教师意见")
opinion_title.style = 'Heading 2'
opinion_title.alignment = WD_ALIGN_PARAGRAPH.LEFT

opinion_space = doc.add_paragraph()
opinion_space.add_run("\n" * 8)

doc.add_paragraph("导师（签字）:                          年     月   日")

# 保存文档
output_path = os.path.join("开题报告", "图书管理系统设计与实现_开题报告.docx")
doc.save(output_path)

print(f"文档已成功生成并保存至: {output_path}") 