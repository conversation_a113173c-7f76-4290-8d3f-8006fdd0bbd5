# 第4章 系统设计

## 4.1 系统架构设计
### 4.1.1 总体架构
本系统采用前后端分离的架构，分为表示层、业务逻辑层和数据访问层三个主要层次。前端使用Vue 3和TypeScript构建用户界面，后端使用Spring Boot框架实现RESTful API服务，数据库选用MySQL存储系统数据。

### 4.1.2 技术架构
- **前端技术栈**：Vue 3、TypeScript、Element Plus、Axios
- **后端技术栈**：Spring Boot、MyBatis
- **数据库**：MySQL 8.0
- **开发工具**：IntelliJ IDEA、Visual Studio Code、Git

### 4.1.3 网络架构
系统采用B/S架构，通过HTTP协议进行前后端通信。系统支持局域网内访问，便于图书馆内部管理使用。

## 4.2 系统模块设计
### 4.2.1 用户认证模块
- **功能描述**：负责用户登录、权限验证等
- **主要组件**：
  - 登录组件：处理用户身份验证
  - 权限控制组件：基于角色的权限管理

### 4.2.2 图书管理模块
- **功能描述**：处理图书信息的CRUD操作
- **主要组件**：
  - 图书信息组件：图书基本信息管理
  - 图书分类组件：图书分类管理
  - 图书检索组件：按书名、作者、ISBN等条件查询

### 4.2.3 借阅管理模块
- **功能描述**：处理图书借阅、归还操作
- **主要组件**：
  - 借书组件：处理借书流程
  - 还书组件：处理还书流程
  - 借阅历史组件：查看借阅记录

### 4.2.4 读者管理模块
- **功能描述**：读者信息管理
- **主要组件**：
  - 读者信息组件：管理读者基本信息

### 4.2.5 统计查询模块
- **功能描述**：基础数据统计
- **主要组件**：
  - 借阅统计组件：统计借阅数量
  - 馆藏统计组件：统计图书馆藏数量

## 4.3 数据库设计
### 4.3.1 ER图
系统主要实体包括用户(User)、图书(Book)、借阅记录(BorrowRecord)、图书分类(Category)等，它们之间存在多种关联关系。

### 4.3.2 数据表设计
#### 用户表(user)
| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 用户ID |
| username | VARCHAR(50) | NOT NULL, UNIQUE | 用户名 |
| password | VARCHAR(255) | NOT NULL | 加密密码 |
| role | VARCHAR(20) | NOT NULL | 角色(ADMIN/LIBRARIAN/READER) |
| name | VARCHAR(100) | | 真实姓名 |
| email | VARCHAR(100) | | 电子邮箱 |
| phone | VARCHAR(20) | | 联系电话 |
| status | TINYINT | NOT NULL, DEFAULT 1 | 状态(1:正常,0:禁用) |
| create_time | DATETIME | NOT NULL | 创建时间 |
| update_time | DATETIME | NOT NULL | 更新时间 |

#### 图书表(book)
| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 图书ID |
| isbn | VARCHAR(20) | NOT NULL, UNIQUE | ISBN编号 |
| title | VARCHAR(200) | NOT NULL | 书名 |
| author | VARCHAR(100) | | 作者 |
| publisher | VARCHAR(100) | | 出版社 |
| publish_date | DATE | | 出版日期 |
| category_id | INT | FOREIGN KEY | 分类ID |
| price | DECIMAL(10,2) | | 价格 |
| stock | INT | NOT NULL, DEFAULT 0 | 库存数量 |
| location | VARCHAR(50) | | 馆藏位置 |
| status | TINYINT | NOT NULL, DEFAULT 1 | 状态(1:可借,0:不可借) |
| description | TEXT | | 图书描述 |
| create_time | DATETIME | NOT NULL | 创建时间 |
| update_time | DATETIME | NOT NULL | 更新时间 |

#### 借阅记录表(borrow_record)
| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 记录ID |
| user_id | INT | FOREIGN KEY | 用户ID |
| book_id | INT | FOREIGN KEY | 图书ID |
| borrow_date | DATETIME | NOT NULL | 借出时间 |
| expected_return_date | DATETIME | NOT NULL | 预期归还时间 |
| actual_return_date | DATETIME | | 实际归还时间 |
| status | TINYINT | NOT NULL | 状态(0:借出,1:已还) |
| create_time | DATETIME | NOT NULL | 创建时间 |
| update_time | DATETIME | NOT NULL | 更新时间 |

#### 图书分类表(category)
| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 分类ID |
| name | VARCHAR(50) | NOT NULL, UNIQUE | 分类名称 |
| create_time | DATETIME | NOT NULL | 创建时间 |
| update_time | DATETIME | NOT NULL | 更新时间 |

## 4.4 界面设计
### 4.4.1 总体风格
系统界面采用简洁、实用的设计风格，以蓝白为主色调，符合图书管理系统的专业性和实用性。整体布局清晰，操作便捷。

### 4.4.2 主要界面设计
1. **登录界面**：简洁的登录表单，支持用户名密码登录
2. **系统主界面**：左侧导航菜单，右侧内容区域
3. **图书管理界面**：包含图书列表、搜索条件、增删改查操作
4. **借阅管理界面**：借还书操作、借阅历史查询
5. **读者管理界面**：读者信息管理
6. **统计查询界面**：基本的统计数据展示

## 4.5 接口设计
### 4.5.1 前后端接口
系统采用RESTful API设计规范，主要接口包括：

#### 用户认证接口
- `POST /api/login`：用户登录
- `POST /api/logout`：用户登出
- `GET /api/user/info`：获取当前用户信息

#### 图书管理接口
- `GET /api/books`：获取图书列表
- `GET /api/books/{id}`：获取图书详情
- `POST /api/books`：新增图书
- `PUT /api/books/{id}`：更新图书信息
- `DELETE /api/books/{id}`：删除图书

#### 借阅管理接口
- `POST /api/borrow`：借书
- `POST /api/return/{recordId}`：还书
- `GET /api/borrow/records`：获取借阅记录

#### 读者管理接口
- `GET /api/readers`：获取读者列表
- `GET /api/readers/{id}`：获取读者详情
- `POST /api/readers`：新增读者
- `PUT /api/readers/{id}`：更新读者信息
- `DELETE /api/readers/{id}`：删除读者

## 4.6 安全设计
### 4.6.1 认证与授权
- 基于Session的用户认证
- 基于角色的权限控制
- 密码加密存储

### 4.6.2 数据安全
- 定期数据备份机制
- 输入验证防止SQL注入
- 数据安全传输

## 4.7 总结
本章详细介绍了图书管理系统的设计方案，包括系统架构、模块设计、数据库设计、界面设计和接口设计等方面。系统设计遵循前后端分离的架构理念，采用主流的技术栈，确保系统的性能和可维护性。通过合理的模块划分和数据结构设计，系统能够满足图书管理的基本功能需求，为图书馆提供有效的管理工具。 