package com.library.management.repository;

import com.library.management.model.entity.Book;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.ArrayList;

import static org.jooq.impl.DSL.*;

/**
 * 图书数据访问层
 */
@Repository
public class BookRepository {

    private final DSLContext dsl;

    @Autowired
    public BookRepository(DSLContext dsl) {
        this.dsl = dsl;
    }

    /**
     * 查找所有图书
     *
     * @return 图书列表
     */
    public List<Book> findAll() {
        return dsl.select()
                .from("book")
                .orderBy(field("id"))
                .fetchInto(Book.class);
    }

    /**
     * 根据ID查找图书
     *
     * @param id 图书ID
     * @return 图书信息（可能为空）
     */
    public Optional<Book> findById(Long id) {
        var result = dsl.select()
                .from("book")
                .where("id = ?", id)
                .fetchOneInto(Book.class);

        return Optional.ofNullable(result);
    }

    /**
     * 搜索图书
     *
     * @param title      书名（可选）
     * @param author     作者（可选）
     * @param categoryId 分类ID（可选）
     * @return 符合条件的图书列表
     */
    public List<Book> search(String title, String author, Long categoryId) {
        // 构建查询条件
        List<String> conditions = new ArrayList<>();
        List<Object> params = new ArrayList<>();
        
        if (title != null && !title.trim().isEmpty()) {
            conditions.add("title like ?");
            params.add("%" + title + "%");
        }
        
        if (author != null && !author.trim().isEmpty()) {
            conditions.add("author like ?");
            params.add("%" + author + "%");
        }
        
        if (categoryId != null) {
            conditions.add("category_id = ?");
            params.add(categoryId);
        }
        
        // 构建SQL查询
        String sql = "SELECT * FROM book";
        if (!conditions.isEmpty()) {
            sql += " WHERE " + String.join(" AND ", conditions);
        }
        sql += " ORDER BY id";
        
        // 执行查询
        return dsl.fetch(sql, params.toArray())
                 .into(Book.class);
    }

    /**
     * 保存图书（新增或更新）
     *
     * @param book 图书信息
     * @return 保存后的图书（包含ID）
     */
    public Book save(Book book) {
        if (book.getId() == null) {
            // 新增图书
            var record = dsl.insertInto(
                            table("book"),
                            field("isbn"),
                            field("title"),
                            field("author"),
                            field("publisher"),
                            field("publish_date"),
                            field("price"),
                            field("description"),
                            field("cover_url"),
                            field("category_id"),
                            field("total_copies"),
                            field("available_copies")
                    )
                    .values(
                            book.getIsbn(),
                            book.getTitle(),
                            book.getAuthor(),
                            book.getPublisher(),
                            book.getPublishDate(),
                            book.getPrice(),
                            book.getDescription(),
                            book.getCoverUrl(),
                            book.getCategoryId(),
                            book.getTotalCopies(),
                            book.getAvailableCopies()
                    )
                    .returning()
                    .fetchOne();

            if (record != null) {
                book.setId(record.getValue(field("id", Long.class)));
            }
        } else {
            // 更新图书
            dsl.update(table("book"))
                    .set(field("isbn"), book.getIsbn())
                    .set(field("title"), book.getTitle())
                    .set(field("author"), book.getAuthor())
                    .set(field("publisher"), book.getPublisher())
                    .set(field("publish_date"), book.getPublishDate())
                    .set(field("price"), book.getPrice())
                    .set(field("description"), book.getDescription())
                    .set(field("cover_url"), book.getCoverUrl())
                    .set(field("category_id"), book.getCategoryId())
                    .set(field("total_copies"), book.getTotalCopies())
                    .set(field("available_copies"), book.getAvailableCopies())
                    .where(field("id").eq(book.getId()))
                    .execute();
        }

        return book;
    }

    /**
     * 删除图书
     *
     * @param id 图书ID
     * @return 是否删除成功
     */
    public boolean deleteById(Long id) {
        return dsl.deleteFrom(table("book"))
                .where(field("id").eq(id))
                .execute() > 0;
    }

    /**
     * 更新图书可借数量
     *
     * @param id        图书ID
     * @param available 新的可借数量
     * @return 是否更新成功
     */
    public boolean updateAvailableCopies(Long id, Integer available) {
        return dsl.update(table("book"))
                .set(field("available_copies"), available)
                .where(field("id").eq(id))
                .execute() > 0;
    }

    /**
     * 根据ISBN查找图书
     *
     * @param isbn ISBN
     * @return 图书信息（可能为空）
     */
    public Optional<Book> findByIsbn(String isbn) {
        var result = dsl.select()
                .from("book")
                .where("isbn = ?", isbn)
                .fetchOneInto(Book.class);

        return Optional.ofNullable(result);
    }
} 