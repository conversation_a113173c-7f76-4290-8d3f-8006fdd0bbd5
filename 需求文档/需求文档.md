图书馆管理系统需求规格说明书
文档版本： V1.1
日期： 2024-05-20
状态： 更新
目录
1. 引言
1.1 目的
1.2 系统范围
1.3 定义与缩略语
1.4 参考资料
2. 系统概述
2.1 系统背景
2.2 系统目标
2.3 用户特征
3. 功能需求
3.1 用户管理模块
3.2 图书信息管理模块
3.3 图书借阅管理模块
3.4 图书检索模块
3.5 统计分析模块
4. 非功能需求
4.1 性能需求
4.2 安全需求
4.3 可靠性需求
4.4 兼容性需求
4.5 可维护性需求
4.6 用户体验需求
5. 系统架构
5.1 总体架构
系统采用前后端分离的架构，由前端应用和后端服务两部分组成，两者通过RESTful API进行通信。

5.2 技术架构
5.2.1 前端技术栈
- 框架：Vue 3
- 类型支持：TypeScript
- 构建工具：Vite
- 状态管理：Pinia
- 路由：Vue Router
- UI组件库：Bootstrap 5
- HTTP客户端：Axios

5.2.2 后端技术栈
- 框架：Spring Boot 2.7.0
- 安全框架：Spring Security
- 数据访问：jOOQ
- 数据库：MySQL 8.0
- 构建工具：Maven

5.3 部署架构
系统采用标准的三层架构部署：
- 表示层：Vue 3前端应用，可部署在静态Web服务器上
- 应用层：Spring Boot后端应用，部署在应用服务器上
- 数据层：MySQL数据库，部署在数据库服务器上

5.4 项目结构
```
bs/
├── library-management-system/       # 后端项目（Spring Boot）
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/                # Java源代码
│   │   │   └── resources/           # 资源文件和配置
│   │   └── test/                    # 测试代码
│   └── pom.xml                      # Maven配置文件
│
├── library-management-system-vue/   # 前端项目（Vue 3）
│   ├── src/
│   │   ├── assets/                  # 静态资源
│   │   ├── components/              # 公共组件
│   │   ├── views/                   # 页面视图
│   │   ├── router/                  # 路由配置
│   │   ├── services/                # API服务
│   │   ├── types/                   # TypeScript类型定义
│   │   ├── config/                  # 配置文件
│   │   ├── App.vue                  # 根组件
│   │   └── main.ts                  # 入口文件
│   ├── public/                      # 公共资源
│   ├── package.json                 # NPM配置
│   └── tsconfig.json                # TypeScript配置
│
├── .gitignore                       # Git忽略文件
└── package.json                     # 根项目配置
```

5.5 版本控制
项目使用Git进行版本控制，并通过`.gitignore`文件配置了以下内容不纳入版本控制：
- `node_modules/` 依赖包目录
- 构建输出目录
- IDE配置文件
- 日志文件和临时文件

6. 数据库设计
6.1 实体关系设计
主要实体及其关系如下：
用户(User) 与 角色(Role) 是多对多关系
图书(Book) 与 借阅记录(BorrowRecord) 是一对多关系
图书(Book) 与 分类(Category) 是多对多关系

6.2 数据表设计
系统主要包含以下数据表：

6.2.1 用户表(User)
| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | BIGINT | 用户ID | 主键 |
| username | VARCHAR(50) | 用户名 | 唯一，非空 |
| password | VARCHAR(100) | 密码（加密存储） | 非空 |
| real_name | VARCHAR(50) | 真实姓名 | 非空 |
| email | VARCHAR(100) | 电子邮箱 | 唯一 |
| phone | VARCHAR(20) | 手机号码 | |
| role | TINYINT | 角色（1-普通用户，2-管理员） | 默认1 |
| status | TINYINT | 账号状态 | 默认1（正常） |
| created_time | DATETIME | 创建时间 | 非空 |
| updated_time | DATETIME | 更新时间 | 非空 |

6.2.2 图书表(Book)
| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | BIGINT | 图书ID | 主键 |
| isbn | VARCHAR(20) | ISBN | 唯一，非空 |
| title | VARCHAR(100) | 书名 | 非空 |
| author | VARCHAR(100) | 作者 | 非空 |
| publisher | VARCHAR(100) | 出版社 | 非空 |
| publish_date | DATE | 出版日期 | |
| price | DECIMAL(10,2) | 价格 | |
| description | TEXT | 图书简介 | |
| cover_url | VARCHAR(200) | 封面图片URL | |
| category_id | BIGINT | 分类ID | 外键 |
| total_copies | INT | 总藏书数量 | 默认0 |
| available_copies | INT | 可借数量 | 默认0 |
| created_time | DATETIME | 创建时间 | 非空 |
| updated_time | DATETIME | 更新时间 | 非空 |

6.2.3 图书分类表(Category)
| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | BIGINT | 分类ID | 主键 |
| category_name | VARCHAR(50) | 分类名称 | 非空 |
| parent_id | BIGINT | 父分类ID | 外键 |
| description | VARCHAR(200) | 分类描述 | |
| created_time | DATETIME | 创建时间 | 非空 |
| updated_time | DATETIME | 更新时间 | 非空 |

6.2.4 借阅记录表(Borrow_Record)
| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | BIGINT | 借阅ID | 主键 |
| user_id | BIGINT | 用户ID | 外键，非空 |
| book_id | BIGINT | 图书ID | 外键，非空 |
| borrow_date | DATETIME | 借阅日期 | 非空 |
| due_date | DATETIME | 应还日期 | 非空 |
| return_date | DATETIME | 实际归还日期 | |
| status | TINYINT | 状态（1-借出，2-已还，3-逾期） | 非空 |
| renew_count | INT | 续借次数 | 默认0 |
| remarks | VARCHAR(200) | 备注 | |
7. 接口设计
7.1 接口规范
系统采用RESTful API规范设计接口，主要特点：
使用HTTP方法表示操作语义（GET、POST、PUT、DELETE）
资源路径使用名词复数形式
使用HTTP状态码表示请求结果
统一的响应格式

7.2 核心接口定义
7.2.1 用户管理接口
| 接口 | 方法 | 描述 |
|-----|------|------|
| /api/users | GET | 获取用户列表 |
| /api/users/{id} | GET | 获取指定用户信息 |
| /api/users | POST | 创建新用户 |
| /api/users/{id} | PUT | 更新用户信息 |
| /api/users/login | POST | 用户登录 |
| /api/users/logout | POST | 用户登出 |

7.2.2 图书管理接口
| 接口 | 方法 | 描述 |
|-----|------|------|
| /api/books | GET | 获取图书列表 |
| /api/books/{id} | GET | 获取指定图书信息 |
| /api/books | POST | 添加新图书 |
| /api/books/{id} | PUT | 更新图书信息 |
| /api/books/{id} | DELETE | 删除图书 |
| /api/books/categories | GET | 获取图书分类 |

7.2.3 借阅管理接口
| 接口 | 方法 | 描述 |
|-----|------|------|
| /api/borrows | GET | 获取借阅记录列表 |
| /api/borrows/{id} | GET | 获取指定借阅记录 |
| /api/borrows | POST | 创建借阅记录(借书) |
| /api/borrows/{id}/return | POST | 图书归还处理 |
| /api/borrows/{id}/renew | POST | 图书续借处理 |
| /api/borrows/user/{userId} | GET | 获取用户借阅记录 |

7.2.4 统计分析接口
| 接口 | 方法 | 描述 |
|-----|------|------|
| /api/stats/borrows | GET | 获取借阅统计数据 |
| /api/stats/books/popular | GET | 获取热门图书排行 |
| /api/stats/reports/export | GET | 导出统计报表 |
8. 项目实施计划
8.1 开发阶段划分
8.1.1 需求分析与设计阶段（2周）
需求分析和确认
系统架构设计
数据库设计
界面原型设计
项目计划制定
8.1.2 开发环境搭建（1周）
- 开发工具安装与配置
- 开发框架搭建
- 版本控制系统配置（Git）
- 测试环境搭建
- 持续集成环境配置
8.1.3 核心功能开发（6周）
数据库实现（1周）
用户认证与权限模块（1周）
图书管理模块（1.5周）
借阅管理模块（1.5周）
检索与统计模块（1周）
8.1.4 系统测试（2周）
单元测试
集成测试
性能测试
安全测试
用户体验测试
问题修复
8.1.5 部署与上线（1周）
生产环境部署
数据迁移（如需）
系统监控配置
用户培训
系统上线
8.2 里程碑计划
| 里程碑 | 计划完成时间 | 交付物 |
|-------|------------|-------|
| 需求分析完成 | Week 2 | 需求规格说明书、系统原型 |
| 系统设计完成 | Week 4 | 系统设计文档、数据库设计文档 |
| 开发环境搭建完成 | Week 5 | 开发环境配置文档、框架代码 |
| 核心功能开发完成 | Week 11 | 核心功能代码、单元测试报告 |
| 系统测试完成 | Week 13 | 测试报告、问题修复记录 |
| 系统上线 | Week 14 | 部署文档、用户手册、培训材料 |
8.3 风险管理
| 风险 | 影响程度 | 可能性 | 应对策略 |
|-----|---------|-------|---------|
| 需求变更频繁 | 高 | 中 | 采用敏捷开发方法，增量式开发；建立需求变更管理流程 |
| 技术难题 | 中 | 中 | 提前进行技术调研；准备备选方案；配置经验丰富的开发人员 |
| 进度延迟 | 高 | 中 | 合理规划任务，留出缓冲时间；定期检查进度，及时调整 |
| 团队协作问题 | 中 | 低 | 建立有效的沟通机制；使用项目管理工具；定期团队会议 |
| 系统性能问题 | 高 | 中 | 早期进行性能测试；制定性能优化策略；预留性能优化时间 |
9. 系统测试计划
9.1 测试类型
9.1.1 功能测试
各模块功能测试
业务流程测试
界面功能测试
数据校验测试
9.1.2 性能测试
负载测试
压力测试
并发测试
稳定性测试
9.1.3 安全测试
身份认证测试
权限控制测试
数据安全测试
防攻击测试
9.1.4 兼容性测试
浏览器兼容性测试
设备兼容性测试
分辨率兼容性测试
9.2 测试环境
硬件环境：与生产环境相似的服务器配置
软件环境：与生产环境相同的操作系统和中间件
网络环境：模拟实际网络环境
测试工具：JUnit, Selenium, JMeter, Postman等
9.3 测试流程
测试计划制定
测试用例设计
测试环境准备
4. 测试执行
缺陷报告与跟踪
缺陷修复验证
测试报告生成
10. 维护与支持计划
10.1 系统维护
定期系统检查
数据库维护与优化
系统性能监控
安全漏洞修复
功能缺陷修复
10.2 系统升级
新功能规划
版本升级计划
兼容性保证
升级流程设计
回滚机制
10.3 用户支持
用户培训
在线帮助系统
问题响应机制
用户反馈收集
常见问题解答
10.4 版本控制管理
- 代码仓库维护
- 分支管理策略
- 版本标记规范
- 代码提交规范
- 合并请求流程
8. 附录
8.1 术语表
| 术语 | 定义 |
|------|------|
| B/S架构 | Browser/Server（浏览器/服务器）架构，指通过浏览器访问服务器应用的系统架构模式 |
| RBAC | Role-Based Access Control（基于角色的访问控制），一种按角色分配权限的访问控制模型 |
| JWT | JSON Web Token，一种基于JSON的开放标准，用于在网络应用间传递声明的安全方法 |
| ORM | Object Relational Mapping（对象关系映射），一种程序设计技术，用于实现面向对象编程语言里不同类型系统的数据之间的转换 |
| jOOQ | Java Object Oriented Querying，一个基于Java的类型安全的SQL查询构建框架，提供与数据库紧密集成的API |
| RESTful API | 一种网络应用程序的设计风格和开发方式，使用HTTP协议中的GET、POST、PUT、DELETE等方法对资源进行操作 |
| ISBN | International Standard Book Number（国际标准书号），图书的国际通用编号 |
8.2 版本历史
| 版本号 | 日期 | 修改说明 | 修改人 |
|-------|------|---------|-------|
| V1.0 | 2024-03-20 | 初始版本 | 系统设计师 |
| V1.1 | 2024-05-20 | 更新版本 | 系统设计师 |
---
文档完成日期：2024年5月20日